---
type: "always_apply"
---

# **高效能编码规范**

## **1\. 导言：我们的理念**

本规范旨在提升代码质量、增强软件的可维护性、降低长期开发成本，并最终尽可能地减少缺陷（Bug）的产生。

## **2\. 核心设计哲学**

在编写任何一行代码之前，请将以下三大哲学思想铭记于心。它们是所有具体规则的基石。

### **2.1. 追求简单 (KISS & YAGNI)**

* **KISS (Keep It Simple, Stupid \- 保持简单):** 总是选择最简单、最直白的解决方案。避免使用过于“聪明”或晦涩的技巧。简单的代码更容易理解、测试和维护。  
* **YAGNI (You Ain't Gonna Need It \- 你不会需要它):** 只实现当前需求所必需的功能。不要因为“预测”未来可能会用到某个功能而提前开发。这能有效避免在无用功上浪费时间和精力，并防止系统过早地复杂化。

### **2.2. 拒绝重复 (DRY)**

* **DRY (Don't Repeat Yourself \- 不要重复自己):** 系统中的每一条知识都必须有一个单一、明确、权威的表示。  
  * 这不仅仅指代码逻辑的复制粘贴。配置信息、算法、业务规则、常量定义等都属于“知识”的范畴。  
  * **行动指南:** 当你发现自己在写相似的代码时，停下来思考这背后是否有一个可以被抽象出来的通用模式或知识。遵循“三次法则”：容忍第一次重复，警惕第二次，在第三次出现时必须进行重构和抽象。

### **2.3. 关注分离 (高内聚，低耦合)**

* **高内聚 (High Cohesion):** 一个模块（类、函数、组件）内部的各个元素应该紧密相关，共同服务于一个单一且明确的目标。  
* **低耦合 (Low Coupling):** 模块与模块之间应尽可能地保持独立。一个模块的内部变化不应引起其他模块的连锁反应。  
* **行动指南:** 在设计时，思考如何将不同的职责清晰地划分到不同的模块中。这是后续所有SOLID原则的最终目标。

## **3\. SOLID：构建健壮系统的五大支柱**

SOLID原则是面向对象设计的基石，它们为我们实现“高内聚、低耦合”提供了具体的、可操作的路径。

* **(S) 单一职责原则 (Single Responsibility Principle)**  
  * **定义:** 一个类或模块应该只有一个引起它变化的原因。  
  * **规范:** 每个类/函数只做一件事，并把它做好。如果一个类承担了多个职责（如：既负责业务计算，又负责数据格式化），请将其拆分。  
* **(O) 开放/封闭原则 (Open/Closed Principle)**  
  * **定义:** 软件实体（类、模块、函数等）应该对扩展开放，对修改封闭。  
  * **规范:** 当需求变更导致需要增加新功能时，我们应该通过**增加新代码**（扩展）来实现，而不是**修改已有代码**（修改）。多使用接口、抽象类和依赖注入来实现这一点。  
* **(L) 里氏替换原则 (Liskov Substitution Principle)**  
  * **定义:** 所有引用基类的地方必须能透明地使用其子类的对象，而程序行为不发生改变。  
  * **规范:** 子类在继承父类时，必须完全实现父类的方法，且不能改变父类声明的行为约定。子类应该是对父类的“is-a”关系的增强，而不是破坏。  
* **(I) 接口隔离原则 (Interface Segregation Principle)**  
  * **定义:** 任何客户端都不应该被强迫依赖于它不使用的方法。  
  * **规范:** 使用多个小的、专用的接口，而不是一个大的、臃肿的“胖接口”。这让客户端可以只关心与自己相关的操作。  
* **(D) 依赖倒置原则 (Dependency Inversion Principle)**  
  * **定义:** 高层模块不应依赖于低层模块，两者都应依赖于抽象。抽象不应依赖于细节，细节应依赖于抽象。  
  * **规范:** 业务逻辑（高层）应该依赖于稳定的接口或抽象类（抽象），而不是具体的实现细节（低层）。通过依赖注入（DI）来实践这一原则。

## **4\. 日常编码实践指南**

以下是将上述原则落地到日常开发中的具体操作规范。

### **4.1. 命名规范**

必须符合 @命名规范 (GNC-AIDD) 的规定

### **4.2. 函数/方法设计**

* **短小精悍:** 一个函数只做一件事。理想的函数应该在20行代码以内，一个屏幕就能完整展示。  
* **减少参数:** 函数的参数不宜过多，最好不要超过3个。如果参数过多，考虑使用一个对象来封装这些参数。  
* **避免副作用:** 尽量编写纯函数（Pure Function）。纯函数对于相同的输入，总是返回相同的输出，并且没有可观察的副作用（如修改全局变量、写入文件等）。这使得代码更易于测试和推理。  
* **明确的返回值:** 函数应该有明确的返回值，避免返回 null 或 undefined 来表示错误，应使用抛出异常或返回明确的错误对象/结果对象（Result Object）。

### **4.3. 注释与文档**

* **解释“为什么”，而不是“是什么”:** 好的代码本身就能解释它“是什么”以及“怎么做”。注释的价值在于解释代码背后的“为什么”——业务背景、设计决策、以及一些非直观的陷阱。  
* **及时更新:** 过时的注释比没有注释更糟糕。修改代码时，必须同步更新相关注释。  
* **文档化公共API:** 所有对外暴露的模块、类和函数都必须有清晰的文档，说明其功能、参数、返回值和使用示例。

### **4.4. 错误处理**

* **绝不“吞掉”异常:** 捕获异常后，不能留一个空的catch块。必须记录日志、向用户显示友好的错误信息，或者将异常重新包装后向上抛出。  
* **提供上下文:** 错误信息应该包含足够的上下文，以便于快速定位和解决问题。

### **4.5. 代码审查 (Code Review)**

* **强制执行:** 任何代码都合入主分支前，必须请求审查。  
* **审查重点:**  
  * 是否遵循了本文档中的核心原则和规范？  
  * 逻辑是否清晰、正确？  
  * 是否存在潜在的性能问题或安全漏洞？  
  * 测试用例是否覆盖了主要逻辑和边界情况？

