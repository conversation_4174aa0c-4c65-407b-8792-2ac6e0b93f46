# 柴管家 Docker 开发环境搭建指南

## 概述

本文档描述如何搭建柴管家项目的 Docker 开发环境。

## 系统要求

- Docker 20.10+
- Docker Compose 1.29+
- 可用端口：7000, 5000, 5432, 6379, 5672, 15672

## 快速启动

### 1. 环境准备

```bash
# 1. 准备环境
python3 scripts/prepare_env.py

# 2. 验证配置
python3 scripts/validate_docker_config.py

# 3. 检查代码语法
python3 scripts/check_syntax.py
```

### 2. 启动服务

```bash
# 使用便捷脚本启动
bash scripts/start_dev.sh

# 或手动启动
docker-compose up --build -d
```

### 3. 验证服务

```bash
# 验证所有服务状态
bash scripts/verify_services.sh

# 或手动检查
curl http://localhost:7000/health
curl http://localhost:7000/api/v1/health/full
```

## 服务说明

### 核心服务

| 服务名 | 端口 | 描述 | 健康检查 |
|--------|------|------|----------|
| backend | 7000 | FastAPI 后端应用 | `/health` |
| db | 5432 | PostgreSQL 数据库 | `pg_isready` |
| redis | 6379 | Redis 缓存 | `redis-cli ping` |
| rabbitmq | 5672/15672 | 消息队列 | `rabbitmqctl status` |
| verification | 5000 | 验证界面 | `/health` |

### 服务访问地址

- **API 文档**: http://localhost:7000/docs
- **后端健康检查**: http://localhost:7000/health
- **验证界面**: http://localhost:5000
- **RabbitMQ 管理**: http://localhost:15672 (用户名/密码: chaiguanjia/password)

## 配置文件说明

### docker-compose.yml

- 定义所有服务容器
- 配置服务依赖关系
- 设置健康检查机制
- 配置数据卷持久化

### backend/Dockerfile

- 基于 Python 3.11-slim
- 安装系统依赖和 Python 包
- 配置非 root 用户运行
- 内置健康检查

### .env 配置

主要环境变量：

```bash
# API配置
API_PORT=7000
API_DEBUG=true

# 数据库配置
DATABASE_URL=*****************************************/chaiguanjia_dev

# Redis配置
REDIS_URL=redis://redis:6379/0

# RabbitMQ配置
RABBITMQ_URL=amqp://chaiguanjia:password@rabbitmq:5672/
```

## 常用命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend

# 停止所有服务
docker-compose down
```

### 开发调试

```bash
# 进入后端容器
docker-compose exec backend bash

# 查看数据库
docker-compose exec db psql -U chaiguanjia -d chaiguanjia_dev

# 重新构建服务
docker-compose up --build backend
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口 7000, 5000, 5432, 6379, 5672, 15672 是否被占用
   - 使用 `netstat -tlnp | grep :7000` 检查端口占用

2. **服务启动失败**
   - 检查 Docker 和 Docker Compose 版本
   - 查看服务日志：`docker-compose logs 服务名`

3. **数据库连接失败**
   - 等待数据库完全启动（约 30 秒）
   - 检查健康检查状态：`docker-compose ps`

4. **依赖包安装失败**
   - 检查网络连接
   - 使用国内镜像源（在 Dockerfile 中配置）

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend

# 实时跟踪日志
docker-compose logs -f --tail=100 backend
```

## 性能优化

### 开发环境优化

1. **使用本地缓存**
   - Docker 构建缓存
   - pip 缓存目录挂载

2. **文件监控优化**
   - 使用 `.dockerignore` 排除不必要文件
   - 配置热重载只监控核心文件

3. **资源限制**
   - 根据开发机器配置适当的资源限制
   - 使用 `docker stats` 监控资源使用

## 部署清单

### 启动前检查

- [ ] Docker 和 Docker Compose 已安装
- [ ] 所需端口未被占用
- [ ] `.env` 文件已创建
- [ ] Python 代码语法正确
- [ ] Docker 配置文件语法正确

### 启动后验证

- [ ] 所有容器正常运行
- [ ] 后端 API 响应正常
- [ ] 数据库连接成功
- [ ] Redis 连接成功
- [ ] RabbitMQ 连接成功
- [ ] 验证界面可访问
- [ ] API 文档可访问

## 下一步

环境搭建完成后，可以继续进行：

1. **FastAPI 应用开发** - 实现业务 API
2. **数据库模型设计** - 创建业务数据表
3. **消息队列集成** - 实现异步任务处理
4. **单元测试编写** - 建立测试体系
5. **CI/CD 配置** - 自动化部署流程

---

> 📝 **注意**: 这是开发环境配置，生产环境需要额外的安全配置和性能优化。