"""
柴管家验证界面
用于验证后端API服务的基本功能
"""

import os
from flask import Flask, render_template, jsonify
import requests

app = Flask(__name__)

# 后端API地址
BACKEND_API_URL = os.environ.get('BACKEND_API_URL', 'http://localhost:8000')

@app.route('/')
def index():
    """主页 - 显示验证界面"""
    return render_template('index.html')

@app.route('/health')
def health():
    """验证界面健康检查"""
    return jsonify({
        'status': 'healthy',
        'service': 'chaiguanjia-verification-ui',
        'backend_url': BACKEND_API_URL
    })

@app.route('/api/verify/backend')
def verify_backend():
    """验证后端服务状态"""
    try:
        response = requests.get(f'{BACKEND_API_URL}/health', timeout=5)
        return jsonify({
            'status': 'success',
            'backend_status': response.status_code,
            'backend_response': response.json()
        })
    except requests.exceptions.RequestException as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'backend_url': BACKEND_API_URL
        }), 500

@app.route('/api/verify/database')
def verify_database():
    """验证数据库连接"""
    try:
        response = requests.get(f'{BACKEND_API_URL}/api/v1/health/db', timeout=5)
        return jsonify({
            'status': 'success',
            'database_status': response.status_code,
            'database_response': response.json()
        })
    except requests.exceptions.RequestException as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'backend_url': BACKEND_API_URL
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)