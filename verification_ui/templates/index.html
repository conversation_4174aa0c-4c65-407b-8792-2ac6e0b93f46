<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家 - 验证界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="card-title mb-0">
                            <i class="fas fa-robot"></i>
                            柴管家 - 系统验证界面
                        </h1>
                    </div>
                    <div class="card-body">
                        <p class="lead">多平台聚合智能客服系统</p>
                        
                        <!-- 系统状态检查 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">后端服务状态</h5>
                                    </div>
                                    <div class="card-body">
                                        <button id="check-backend" class="btn btn-primary">检查后端服务</button>
                                        <div id="backend-result" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">数据库连接</h5>
                                    </div>
                                    <div class="card-body">
                                        <button id="check-database" class="btn btn-success">检查数据库</button>
                                        <div id="database-result" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- API文档链接 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning">
                                        <h5 class="mb-0">快速链接</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2 d-md-flex">
                                            <a href="http://localhost:7000/docs" target="_blank" class="btn btn-outline-primary">
                                                API 文档 (Swagger)
                                            </a>
                                            <a href="http://localhost:7000/redoc" target="_blank" class="btn btn-outline-info">
                                                API 文档 (ReDoc)
                                            </a>
                                            <a href="http://localhost:15672" target="_blank" class="btn btn-outline-secondary">
                                                RabbitMQ 管理
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 系统信息 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">系统信息</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>项目版本:</strong> 1.0.0 (Sprint 0)<br>
                                                <strong>架构:</strong> 模块化单体 + 事件驱动<br>
                                                <strong>后端框架:</strong> FastAPI + Python 3.11
                                            </div>
                                            <div class="col-md-6">
                                                <strong>数据库:</strong> PostgreSQL 15<br>
                                                <strong>消息队列:</strong> RabbitMQ 3.12<br>
                                                <strong>缓存:</strong> Redis 7
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>