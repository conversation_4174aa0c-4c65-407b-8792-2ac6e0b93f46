// 柴管家验证界面交互脚本

document.addEventListener('DOMContentLoaded', function() {
    // 后端服务检查
    document.getElementById('check-backend').addEventListener('click', function() {
        const button = this;
        const resultDiv = document.getElementById('backend-result');
        
        button.disabled = true;
        button.textContent = '检查中...';
        resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"></div> 正在检查后端服务...';
        
        fetch('/api/verify/backend')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>后端服务正常</strong><br>
                            状态码: ${data.backend_status}<br>
                            服务: ${data.backend_response.service || 'chaiguanjia-api'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>后端服务异常</strong><br>
                            错误: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle"></i>
                        <strong>连接失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = '检查后端服务';
            });
    });
    
    // 数据库连接检查
    document.getElementById('check-database').addEventListener('click', function() {
        const button = this;
        const resultDiv = document.getElementById('database-result');
        
        button.disabled = true;
        button.textContent = '检查中...';
        resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm text-success" role="status"></div> 正在检查数据库连接...';
        
        fetch('/api/verify/database')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-database"></i>
                            <strong>数据库连接正常</strong><br>
                            状态码: ${data.database_status}<br>
                            连接状态: ${data.database_response.database || '已连接'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>数据库连接异常</strong><br>
                            错误: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle"></i>
                        <strong>连接失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = '检查数据库';
            });
    });
});

// 页面加载完成后自动检查系统状态
window.addEventListener('load', function() {
    // 延迟2秒后自动检查，给后端服务启动时间
    setTimeout(() => {
        document.getElementById('check-backend').click();
        setTimeout(() => {
            document.getElementById('check-database').click();
        }, 1000);
    }, 2000);
});