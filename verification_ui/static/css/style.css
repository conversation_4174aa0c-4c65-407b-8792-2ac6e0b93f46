/* 柴管家验证界面样式 */

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    border-radius: 0 !important;
}

.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner-border {
    width: 1rem;
    height: 1rem;
}

.lead {
    color: #6c757d;
    font-weight: 300;
}

/* 状态指示器动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.alert-success {
    animation: pulse 0.5s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        margin-top: 2rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* 自定义图标颜色 */
.fa-robot {
    color: #ffc107;
}

.fa-check-circle {
    color: #28a745;
}

.fa-exclamation-triangle {
    color: #ffc107;
}

.fa-times-circle {
    color: #dc3545;
}

.fa-database {
    color: #17a2b8;
}