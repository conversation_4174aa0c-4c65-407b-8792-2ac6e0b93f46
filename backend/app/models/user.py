"""
用户相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel, TenantMixin, StatusMixin


class User(BaseModel, TenantMixin, StatusMixin):
    """用户模型"""
    
    __tablename__ = "users"
    
    # 基本信息
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        comment="邮箱地址"
    )
    
    username = Column(
        String(100),
        unique=True,
        nullable=True,
        index=True,
        comment="用户名"
    )
    
    full_name = Column(
        String(200),
        nullable=False,
        comment="姓名"
    )
    
    # 认证信息
    password_hash = Column(
        String(255),
        nullable=False,
        comment="密码哈希"
    )
    
    salt = Column(
        String(100),
        nullable=False,
        comment="密码盐值"
    )
    
    # 账户状态
    is_email_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="邮箱是否已验证"
    )
    
    email_verified_at = Column(
        DateTime,
        nullable=True,
        comment="邮箱验证时间"
    )
    
    is_locked = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="账户是否被锁定"
    )
    
    locked_until = Column(
        DateTime,
        nullable=True,
        comment="锁定到期时间"
    )
    
    # 登录信息
    last_login_at = Column(
        DateTime,
        nullable=True,
        comment="最后登录时间"
    )
    
    last_login_ip = Column(
        String(45),
        nullable=True,
        comment="最后登录IP"
    )
    
    login_attempts = Column(
        "login_attempts",
        String(20),
        default="0",
        nullable=False,
        comment="失败登录尝试次数"
    )
    
    # 用户配置
    timezone = Column(
        String(50),
        default="Asia/Shanghai",
        nullable=False,
        comment="时区"
    )
    
    language = Column(
        String(10),
        default="zh-CN",
        nullable=False,
        comment="语言偏好"
    )
    
    # 用户偏好设置（JSON格式）
    preferences = Column(
        JSON,
        default=dict,
        nullable=True,
        comment="用户偏好设置"
    )
    
    # 头像
    avatar_url = Column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    # 个人简介
    bio = Column(
        Text,
        nullable=True,
        comment="个人简介"
    )
    
    # 手机号
    phone = Column(
        String(20),
        nullable=True,
        comment="手机号码"
    )
    
    is_phone_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="手机号是否已验证"
    )
    
    # 关系
    # channel_instances = relationship(
    #     "ChannelInstance",
    #     back_populates="user",
    #     cascade="all, delete-orphan"
    # )
    
    # conversations = relationship(
    #     "Conversation",
    #     back_populates="user",
    #     cascade="all, delete-orphan"
    # )
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, full_name={self.full_name})>"
    
    def is_password_correct(self, password: str) -> bool:
        """验证密码是否正确"""
        # TODO: 实现密码验证逻辑
        # 这里应该使用passlib或类似的库来验证密码
        return True
    
    def set_password(self, password: str):
        """设置密码"""
        # TODO: 实现密码加密逻辑
        # 这里应该使用passlib或类似的库来加密密码
        import hashlib
        import secrets
        
        # 生成盐值
        self.salt = secrets.token_hex(16)
        
        # 计算密码哈希
        password_with_salt = password + self.salt
        self.password_hash = hashlib.sha256(password_with_salt.encode()).hexdigest()
    
    def verify_email(self):
        """验证邮箱"""
        self.is_email_verified = True
        self.email_verified_at = datetime.utcnow()
    
    def lock_account(self, until: datetime = None):
        """锁定账户"""
        self.is_locked = True
        self.locked_until = until
    
    def unlock_account(self):
        """解锁账户"""
        self.is_locked = False
        self.locked_until = None
        self.login_attempts = "0"
    
    def record_login(self, ip_address: str = None):
        """记录登录信息"""
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip_address
        self.login_attempts = "0"  # 重置失败尝试次数
    
    def increment_login_attempts(self):
        """增加登录失败次数"""
        current_attempts = int(self.login_attempts or "0")
        self.login_attempts = str(current_attempts + 1)
    
    def should_lock_account(self, max_attempts: int = 5) -> bool:
        """判断是否应该锁定账户"""
        current_attempts = int(self.login_attempts or "0")
        return current_attempts >= max_attempts
    
    def update_preferences(self, new_preferences: dict):
        """更新用户偏好设置"""
        if self.preferences is None:
            self.preferences = {}
        
        self.preferences.update(new_preferences)
    
    def get_preference(self, key: str, default=None):
        """获取用户偏好设置"""
        if self.preferences is None:
            return default
        return self.preferences.get(key, default)
    
    def to_dict(self, exclude_sensitive=True):
        """转换为字典，可选择排除敏感信息"""
        exclude_fields = []
        if exclude_sensitive:
            exclude_fields = ['password_hash', 'salt', 'login_attempts']
        
        return super().to_dict(exclude_fields=exclude_fields)
    
    @property 
    def display_name(self) -> str:
        """显示名称"""
        return self.full_name or self.username or self.email.split('@')[0]
    
    @property
    def is_account_locked(self) -> bool:
        """账户是否被锁定"""
        if not self.is_locked:
            return False
        
        if self.locked_until is None:
            return True
        
        return datetime.utcnow() < self.locked_until