"""
渠道相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, ForeignKey, Integer
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum

from app.models.base import BaseModel, TenantMixin, StatusMixin, UserTrackingMixin, GUID


class PlatformType(str, Enum):
    """平台类型枚举"""
    XIANYU = "xianyu"
    WECHAT = "wechat"
    WEIBO = "weibo"
    XIAOHONGSHU = "xiaohongshu"
    DOUYIN = "douyin"
    TAOBAO = "taobao"
    QQ = "qq"
    CUSTOM = "custom"


class SyncStatus(str, Enum):
    """同步状态枚举"""
    PENDING = "pending"
    SYNCING = "syncing"
    SUCCESS = "success"
    FAILED = "failed"
    STOPPED = "stopped"


class ChannelInstance(BaseModel, TenantMixin, StatusMixin, UserTrackingMixin):
    """渠道实例模型"""
    
    __tablename__ = "channel_instances"
    
    # 关联用户
    user_id = Column(
        GUID(),
        ForeignKey("users.id"),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    # 平台信息
    platform = Column(
        String(50),
        nullable=False,
        index=True,
        comment="平台类型"
    )
    
    platform_account_id = Column(
        String(200),
        nullable=True,
        comment="平台账号ID"
    )
    
    platform_username = Column(
        String(200),
        nullable=True,
        comment="平台用户名"
    )
    
    # 渠道配置
    alias = Column(
        String(200),
        nullable=False,
        comment="渠道别名"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="渠道描述"
    )
    
    # 认证凭证（加密存储）
    credentials = Column(
        JSON,
        nullable=False,
        comment="认证凭证（加密）"
    )
    
    # 渠道配置
    config = Column(
        JSON,
        default=dict,
        nullable=True,
        comment="渠道配置"
    )
    
    # 同步相关
    sync_status = Column(
        String(50),
        default=SyncStatus.PENDING.value,
        nullable=False,
        comment="同步状态"
    )
    
    last_sync_at = Column(
        DateTime,
        nullable=True,
        comment="最后同步时间"
    )
    
    next_sync_at = Column(
        DateTime,
        nullable=True,
        comment="下次同步时间"
    )
    
    sync_interval = Column(
        Integer,
        default=300,  # 5分钟
        nullable=False,
        comment="同步间隔（秒）"
    )
    
    sync_error_message = Column(
        Text,
        nullable=True,
        comment="同步错误信息"
    )
    
    sync_error_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="连续同步错误次数"
    )
    
    # 统计信息
    total_messages = Column(
        Integer,
        default=0,
        nullable=False,
        comment="总消息数"
    )
    
    unread_messages = Column(
        Integer,
        default=0,
        nullable=False,
        comment="未读消息数"
    )
    
    last_message_at = Column(
        DateTime,
        nullable=True,
        comment="最后消息时间"
    )
    
    # 性能指标
    avg_response_time = Column(
        "avg_response_time",
        String(20),
        nullable=True,
        comment="平均响应时间（秒）"
    )
    
    response_rate = Column(
        "response_rate", 
        String(20),
        nullable=True,
        comment="响应率（百分比）"
    )
    
    # 关系
    # user = relationship("User", back_populates="channel_instances")
    # conversations = relationship(
    #     "Conversation",
    #     back_populates="channel_instance",
    #     cascade="all, delete-orphan"
    # )
    
    def __repr__(self):
        return f"<ChannelInstance(id={self.id}, platform={self.platform}, alias={self.alias})>"
    
    def is_platform_supported(self) -> bool:
        """检查平台是否支持"""
        supported_platforms = [p.value for p in PlatformType]
        return self.platform in supported_platforms
    
    def can_sync(self) -> bool:
        """检查是否可以同步"""
        if not self.is_active:
            return False
        
        if self.sync_status == SyncStatus.SYNCING.value:
            return False
        
        # 检查是否有过多连续错误
        if self.sync_error_count >= 5:
            return False
        
        return True
    
    def start_sync(self):
        """开始同步"""
        self.sync_status = SyncStatus.SYNCING.value
        self.last_sync_at = datetime.utcnow()
    
    def complete_sync(self, success: bool, error_message: str = None):
        """完成同步"""
        if success:
            self.sync_status = SyncStatus.SUCCESS.value
            self.sync_error_count = 0
            self.sync_error_message = None
        else:
            self.sync_status = SyncStatus.FAILED.value
            self.sync_error_count += 1
            self.sync_error_message = error_message
        
        # 计算下次同步时间
        self.calculate_next_sync()
    
    def calculate_next_sync(self):
        """计算下次同步时间"""
        import datetime as dt
        
        # 如果有错误，延长同步间隔
        if self.sync_error_count > 0:
            # 指数退避策略
            delay_multiplier = min(2 ** self.sync_error_count, 32)  # 最大32倍延迟
            actual_interval = self.sync_interval * delay_multiplier
        else:
            actual_interval = self.sync_interval
        
        self.next_sync_at = datetime.utcnow() + dt.timedelta(seconds=actual_interval)
    
    def update_message_stats(self, total: int = None, unread: int = None, last_message_at: datetime = None):
        """更新消息统计"""
        if total is not None:
            self.total_messages = total
        
        if unread is not None:
            self.unread_messages = unread
        
        if last_message_at is not None:
            self.last_message_at = last_message_at
    
    def get_credential(self, key: str, default=None):
        """获取认证凭证"""
        if not self.credentials:
            return default
        # TODO: 这里应该解密凭证
        return self.credentials.get(key, default)
    
    def set_credential(self, key: str, value: str):
        """设置认证凭证"""
        if not self.credentials:
            self.credentials = {}
        # TODO: 这里应该加密凭证
        self.credentials[key] = value
    
    def get_config(self, key: str, default=None):
        """获取配置项"""
        if not self.config:
            return default
        return self.config.get(key, default)
    
    def set_config(self, key: str, value):
        """设置配置项"""
        if not self.config:
            self.config = {}
        self.config[key] = value
    
    def update_config(self, new_config: dict):
        """更新配置"""
        if not self.config:
            self.config = {}
        self.config.update(new_config)
    
    def to_dict(self, exclude_sensitive=True):
        """转换为字典，可选择排除敏感信息"""
        exclude_fields = []
        if exclude_sensitive:
            exclude_fields = ['credentials']
        
        result = super().to_dict(exclude_fields=exclude_fields)
        
        # 添加计算字段
        result['platform_display_name'] = self.get_platform_display_name()
        result['sync_status_display'] = self.get_sync_status_display()
        result['can_sync'] = self.can_sync()
        
        return result
    
    def get_platform_display_name(self) -> str:
        """获取平台显示名称"""
        platform_names = {
            PlatformType.XIANYU.value: "闲鱼",
            PlatformType.WECHAT.value: "微信",
            PlatformType.WEIBO.value: "微博",
            PlatformType.XIAOHONGSHU.value: "小红书",
            PlatformType.DOUYIN.value: "抖音",
            PlatformType.TAOBAO.value: "淘宝",
            PlatformType.QQ.value: "QQ",
            PlatformType.CUSTOM.value: "自定义",
        }
        return platform_names.get(self.platform, self.platform)
    
    def get_sync_status_display(self) -> str:
        """获取同步状态显示名称"""
        status_names = {
            SyncStatus.PENDING.value: "等待同步",
            SyncStatus.SYNCING.value: "同步中",
            SyncStatus.SUCCESS.value: "同步成功",
            SyncStatus.FAILED.value: "同步失败",
            SyncStatus.STOPPED.value: "已停止",
        }
        return status_names.get(self.sync_status, self.sync_status)
    
    @property
    def is_healthy(self) -> bool:
        """渠道是否健康"""
        return (
            self.is_active and
            self.sync_error_count < 3 and
            self.sync_status != SyncStatus.FAILED.value
        )
    
    @property
    def needs_attention(self) -> bool:
        """是否需要关注"""
        return (
            self.sync_error_count >= 3 or
            self.sync_status == SyncStatus.FAILED.value or
            not self.is_active
        )