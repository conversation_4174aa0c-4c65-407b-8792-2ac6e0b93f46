"""
基础数据模型类
"""

import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import Column, String, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.types import TypeDecorator, CHAR


class GUID(TypeDecorator):
    """平台无关的GUID类型"""
    
    impl = CHAR
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID())
        else:
            return dialect.type_descriptor(CHAR(36))
    
    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(uuid.UUID(value))
            return str(value)
    
    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value


@as_declarative()
class BaseModel:
    """所有模型的基类"""
    
    id: Any
    __name__: str
    
    # 生成表名（小写+下划线）
    @declared_attr
    def __tablename__(cls) -> str:
        # 将驼峰命名转换为下划线命名
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
        return name
    
    # 主键
    id = Column(
        GUID(),
        primary_key=True,
        default=uuid.uuid4,
        unique=True,
        nullable=False,
        comment="主键ID"
    )
    
    # 创建时间
    created_at = Column(
        DateTime,
        default=datetime.utcnow,
        nullable=False,
        comment="创建时间"
    )
    
    # 更新时间
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )
    
    # 软删除标记
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    # 删除时间
    deleted_at = Column(
        DateTime,
        nullable=True,
        comment="删除时间"
    )
    
    # 备注字段
    remarks = Column(
        Text,
        nullable=True,
        comment="备注信息"
    )
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self, exclude_fields=None):
        """转换为字典格式"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                
                # 处理特殊类型
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                elif isinstance(value, uuid.UUID):
                    result[column.name] = str(value)
                else:
                    result[column.name] = value
        
        return result
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """恢复软删除"""
        self.is_deleted = False
        self.deleted_at = None
    
    @classmethod
    def create_with_defaults(cls, **kwargs):
        """使用默认值创建实例"""
        # 设置默认的创建和更新时间
        now = datetime.utcnow()
        kwargs.setdefault('created_at', now)
        kwargs.setdefault('updated_at', now)
        kwargs.setdefault('is_deleted', False)
        
        return cls(**kwargs)


class TenantMixin:
    """多租户支持混入类"""
    
    # 租户ID（为未来SaaS化预留）
    tenant_id = Column(
        GUID(),
        nullable=True,
        comment="租户ID"
    )


class UserTrackingMixin:
    """用户操作追踪混入类"""
    
    # 创建者ID
    created_by = Column(
        GUID(),
        nullable=True,
        comment="创建者ID"
    )
    
    # 更新者ID
    updated_by = Column(
        GUID(),
        nullable=True,
        comment="更新者ID"
    )


class VersionMixin:
    """版本控制混入类"""
    
    # 版本号
    version = Column(
        "version",
        String(50),
        default="1.0",
        nullable=False,
        comment="版本号"
    )


class StatusMixin:
    """状态管理混入类"""
    
    # 状态
    status = Column(
        String(50),
        default="active",
        nullable=False,
        comment="状态"
    )
    
    # 是否激活
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )