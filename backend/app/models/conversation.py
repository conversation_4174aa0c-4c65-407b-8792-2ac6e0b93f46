"""
会话和消息相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, ForeignKey, Integer, Index
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum

from app.models.base import BaseModel, TenantMixin, StatusMixin, UserTrackingMixin, GUID


class ConversationStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    PENDING = "pending"
    RESOLVED = "resolved"
    ARCHIVED = "archived"
    CLOSED = "closed"


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    FILE = "file"
    SYSTEM = "system"
    STICKER = "sticker"
    LOCATION = "location"


class MessageDirection(str, Enum):
    """消息方向枚举"""
    INBOUND = "inbound"   # 客户发送给我们
    OUTBOUND = "outbound" # 我们发送给客户


class MessageStatus(str, Enum):
    """消息状态枚举"""
    PENDING = "pending"     # 待发送
    SENDING = "sending"     # 发送中
    SENT = "sent"          # 已发送
    DELIVERED = "delivered" # 已送达
    READ = "read"          # 已读
    FAILED = "failed"      # 发送失败


class Conversation(BaseModel, TenantMixin, StatusMixin, UserTrackingMixin):
    """会话模型"""
    
    __tablename__ = "conversations"
    
    # 关联用户和渠道
    user_id = Column(
        GUID(),
        ForeignKey("users.id"),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    channel_instance_id = Column(
        GUID(),
        ForeignKey("channel_instances.id"),
        nullable=False,
        index=True,
        comment="渠道实例ID"
    )
    
    # 客户信息
    customer_id = Column(
        String(200),
        nullable=False,
        comment="客户ID（来自第三方平台）"
    )
    
    customer_name = Column(
        String(200),
        nullable=True,
        comment="客户姓名"
    )
    
    customer_avatar = Column(
        String(500),
        nullable=True,
        comment="客户头像URL"
    )
    
    customer_info = Column(
        JSON,
        default=dict,
        nullable=True,
        comment="客户详细信息"
    )
    
    # 会话信息
    title = Column(
        String(500),
        nullable=True,
        comment="会话标题"
    )
    
    conversation_status = Column(
        String(50),
        default=ConversationStatus.ACTIVE.value,
        nullable=False,
        comment="会话状态"
    )
    
    # 第三方平台的会话ID
    external_conversation_id = Column(
        String(200),
        nullable=True,
        comment="第三方平台会话ID"
    )
    
    # 标签和分类
    tags = Column(
        JSON,
        default=list,
        nullable=True,
        comment="会话标签"
    )
    
    category = Column(
        String(100),
        nullable=True,
        comment="会话分类"
    )
    
    priority = Column(
        String(20),
        default="normal",
        nullable=False,
        comment="优先级"
    )
    
    # 消息统计
    total_messages = Column(
        Integer,
        default=0,
        nullable=False,
        comment="总消息数"
    )
    
    unread_messages = Column(
        Integer,
        default=0,
        nullable=False,
        comment="未读消息数"
    )
    
    # 最后消息信息
    last_message_id = Column(
        GUID(),
        nullable=True,
        comment="最后一条消息ID"
    )
    
    last_message_content = Column(
        Text,
        nullable=True,
        comment="最后一条消息内容"
    )
    
    last_message_at = Column(
        DateTime,
        nullable=True,
        index=True,
        comment="最后消息时间"
    )
    
    last_message_direction = Column(
        String(20),
        nullable=True,
        comment="最后消息方向"
    )
    
    # 服务相关
    assigned_to = Column(
        GUID(),
        nullable=True,
        comment="分配给的客服人员ID"
    )
    
    first_response_at = Column(
        DateTime,
        nullable=True,
        comment="首次响应时间"
    )
    
    resolved_at = Column(
        DateTime,
        nullable=True,
        comment="解决时间"
    )
    
    # AI相关
    ai_enabled = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否启用AI"
    )
    
    ai_confidence_threshold = Column(
        "ai_confidence_threshold",
        String(10),
        default="0.8",
        nullable=False,
        comment="AI置信度阈值"
    )
    
    # 关系
    # user = relationship("User", back_populates="conversations")
    # channel_instance = relationship("ChannelInstance", back_populates="conversations")
    # messages = relationship(
    #     "Message",
    #     back_populates="conversation",
    #     cascade="all, delete-orphan",
    #     order_by="Message.created_at"
    # )
    
    # 索引
    __table_args__ = (
        Index('idx_conversation_customer', 'channel_instance_id', 'customer_id'),
        Index('idx_conversation_status_time', 'conversation_status', 'last_message_at'),
        Index('idx_conversation_unread', 'unread_messages'),
    )
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, customer={self.customer_name}, status={self.conversation_status})>"
    
    def add_tag(self, tag: str):
        """添加标签"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str):
        """移除标签"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
    
    def mark_as_read(self):
        """标记为已读"""
        self.unread_messages = 0
    
    def increment_unread(self):
        """增加未读消息数"""
        self.unread_messages += 1
    
    def update_last_message(self, message_id: str, content: str, direction: str, timestamp: datetime = None):
        """更新最后消息信息"""
        self.last_message_id = message_id
        self.last_message_content = content[:500]  # 截断长内容
        self.last_message_direction = direction
        self.last_message_at = timestamp or datetime.utcnow()
    
    def assign_to(self, user_id: str):
        """分配给客服人员"""
        self.assigned_to = user_id
    
    def resolve(self):
        """解决会话"""
        self.conversation_status = ConversationStatus.RESOLVED.value
        self.resolved_at = datetime.utcnow()
    
    def archive(self):
        """归档会话"""
        self.conversation_status = ConversationStatus.ARCHIVED.value
    
    def close(self):
        """关闭会话"""
        self.conversation_status = ConversationStatus.CLOSED.value
    
    def reopen(self):
        """重新打开会话"""
        self.conversation_status = ConversationStatus.ACTIVE.value
        self.resolved_at = None
    
    @property
    def is_active(self) -> bool:
        """会话是否活跃"""
        return self.conversation_status == ConversationStatus.ACTIVE.value
    
    @property
    def needs_response(self) -> bool:
        """是否需要回复"""
        return (
            self.is_active and 
            self.last_message_direction == MessageDirection.INBOUND.value and
            self.unread_messages > 0
        )
    
    @property
    def response_time(self) -> float:
        """响应时间（分钟）"""
        if not self.first_response_at or not self.created_at:
            return 0.0
        
        delta = self.first_response_at - self.created_at
        return delta.total_seconds() / 60


class Message(BaseModel, TenantMixin):
    """消息模型"""
    
    __tablename__ = "messages"
    
    # 关联会话
    conversation_id = Column(
        GUID(),
        ForeignKey("conversations.id"),
        nullable=False,
        index=True,
        comment="会话ID"
    )
    
    # 消息基本信息
    external_message_id = Column(
        String(200),
        nullable=True,
        comment="第三方平台消息ID"
    )
    
    message_type = Column(
        String(50),
        default=MessageType.TEXT.value,
        nullable=False,
        comment="消息类型"
    )
    
    direction = Column(
        String(20),
        nullable=False,
        comment="消息方向"
    )
    
    # 发送者信息
    sender_id = Column(
        String(200),
        nullable=False,
        comment="发送者ID"
    )
    
    sender_name = Column(
        String(200),
        nullable=True,
        comment="发送者姓名"
    )
    
    sender_avatar = Column(
        String(500),
        nullable=True,
        comment="发送者头像"
    )
    
    # 消息内容
    content = Column(
        Text,
        nullable=False,
        comment="消息内容"
    )
    
    # 媒体文件信息
    media_url = Column(
        String(500),
        nullable=True,
        comment="媒体文件URL"
    )
    
    media_size = Column(
        Integer,
        nullable=True,
        comment="媒体文件大小（字节）"
    )
    
    media_duration = Column(
        Integer,
        nullable=True,
        comment="媒体时长（秒）"
    )
    
    # 消息状态
    message_status = Column(
        String(50),
        default=MessageStatus.PENDING.value,
        nullable=False,
        comment="消息状态"
    )
    
    # 回复相关
    reply_to_id = Column(
        GUID(),
        ForeignKey("messages.id"),
        nullable=True,
        comment="回复的消息ID"
    )
    
    # 时间戳
    sent_at = Column(
        DateTime,
        nullable=True,
        comment="发送时间"
    )
    
    delivered_at = Column(
        DateTime,
        nullable=True,
        comment="送达时间"
    )
    
    read_at = Column(
        DateTime,
        nullable=True,
        comment="阅读时间"
    )
    
    # 元数据
    metadata = Column(
        JSON,
        default=dict,
        nullable=True,
        comment="消息元数据"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # AI相关
    ai_generated = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否由AI生成"
    )
    
    ai_confidence = Column(
        "ai_confidence",
        String(10),
        nullable=True,
        comment="AI置信度"
    )
    
    ai_sources = Column(
        JSON,
        default=list,
        nullable=True,
        comment="AI回复来源"
    )
    
    # 关系
    # conversation = relationship("Conversation", back_populates="messages")
    # reply_to = relationship("Message", remote_side=[id])
    
    # 索引
    __table_args__ = (
        Index('idx_message_conversation_time', 'conversation_id', 'created_at'),
        Index('idx_message_status', 'message_status'),
        Index('idx_message_direction_time', 'direction', 'created_at'),
        Index('idx_message_external_id', 'external_message_id'),
    )
    
    def __repr__(self):
        return f"<Message(id={self.id}, type={self.message_type}, direction={self.direction})>"
    
    def mark_as_sent(self):
        """标记为已发送"""
        self.message_status = MessageStatus.SENT.value
        self.sent_at = datetime.utcnow()
    
    def mark_as_delivered(self):
        """标记为已送达"""
        self.message_status = MessageStatus.DELIVERED.value
        self.delivered_at = datetime.utcnow()
    
    def mark_as_read(self):
        """标记为已读"""
        self.message_status = MessageStatus.READ.value
        self.read_at = datetime.utcnow()
    
    def mark_as_failed(self, error: str):
        """标记为失败"""
        self.message_status = MessageStatus.FAILED.value
        self.error_message = error
    
    def set_ai_info(self, confidence: float, sources: list = None):
        """设置AI信息"""
        self.ai_generated = True
        self.ai_confidence = str(confidence)
        self.ai_sources = sources or []
    
    def get_metadata(self, key: str, default=None):
        """获取元数据"""
        if not self.metadata:
            return default
        return self.metadata.get(key, default)
    
    def set_metadata(self, key: str, value):
        """设置元数据"""
        if not self.metadata:
            self.metadata = {}
        self.metadata[key] = value
    
    @property
    def is_inbound(self) -> bool:
        """是否为入站消息"""
        return self.direction == MessageDirection.INBOUND.value
    
    @property
    def is_outbound(self) -> bool:
        """是否为出站消息"""
        return self.direction == MessageDirection.OUTBOUND.value
    
    @property
    def is_media(self) -> bool:
        """是否为媒体消息"""
        return self.message_type in [
            MessageType.IMAGE.value,
            MessageType.VIDEO.value,
            MessageType.AUDIO.value,
            MessageType.FILE.value
        ]
    
    @property
    def display_content(self) -> str:
        """显示内容"""
        if self.message_type == MessageType.TEXT.value:
            return self.content
        elif self.message_type == MessageType.IMAGE.value:
            return "[图片]"
        elif self.message_type == MessageType.VIDEO.value:
            return "[视频]"
        elif self.message_type == MessageType.AUDIO.value:
            return "[语音]"
        elif self.message_type == MessageType.FILE.value:
            return "[文件]"
        elif self.message_type == MessageType.STICKER.value:
            return "[表情]"
        elif self.message_type == MessageType.LOCATION.value:
            return "[位置]"
        else:
            return self.content or "[未知消息类型]"