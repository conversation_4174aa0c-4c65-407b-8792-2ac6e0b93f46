"""
柴管家 FastAPI 主应用
多平台聚合智能客服系统
"""

from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.config import settings
from app.core.logging import setup_logging, LoggingMiddleware, get_logger
from app.core.database import engine
from app.api.v1 import api_router


# 设置日志
setup_logging()
logger = get_logger("app.main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("柴管家 API 启动中...", version="1.0.0")
    
    # 验证数据库连接
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("数据库连接验证成功")
    except Exception as e:
        logger.error("数据库连接验证失败", error=str(e))
        raise
    
    logger.info("柴管家 API 启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("柴管家 API 正在关闭...")
    logger.info("柴管家 API 已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="柴管家 API",
    description="多平台聚合智能客服系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    contact={
        "name": "柴管家团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
    },
)

# 添加自定义中间件（顺序很重要）

# 1. 日志中间件
app.add_middleware(LoggingMiddleware)

# 2. GZIP压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 3. CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 4. 受信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)


# 异常处理器
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    logger.error(
        "HTTP异常",
        status_code=exc.status_code,
        detail=exc.detail,
        url=str(request.url),
        method=request.method,
    )
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP_ERROR",
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat(),
        },
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(
        "请求验证异常",
        errors=exc.errors(),
        url=str(request.url),
        method=request.method,
    )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "VALIDATION_ERROR",
            "message": "请求参数验证失败",
            "details": exc.errors(),
            "timestamp": datetime.now().isoformat(),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(
        "服务器内部异常",
        error=str(exc),
        error_type=type(exc).__name__,
        url=str(request.url),
        method=request.method,
    )
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "服务器内部错误，请稍后重试",
            "timestamp": datetime.now().isoformat(),
        },
    )


# 注册API路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """根端点 - 返回API基本信息"""
    logger.info("根端点被访问")
    return {
        "message": "柴管家 API v1.0.0", 
        "status": "running",
        "version": "1.0.0",
        "description": "多平台聚合智能客服系统",
        "docs_url": "/docs",
        "health_check": "/health",
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    logger.debug("健康检查端点被访问")
    return {
        "status": "healthy", 
        "version": "1.0.0",
        "service": "chaiguanjia-api",
        "environment": "development" if settings.API_DEBUG else "production",
    }