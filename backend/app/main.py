"""
柴管家 FastAPI 主应用
多平台聚合智能客服系统
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1 import api_router

# 设置日志
setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title="柴管家 API",
    description="多平台聚合智能客服系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    """根端点 - 返回API基本信息"""
    return {
        "message": "柴管家 API v1.0.0", 
        "status": "running",
        "version": "1.0.0",
        "description": "多平台聚合智能客服系统"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy", 
        "version": "1.0.0",
        "service": "chaiguanjia-api"
    }