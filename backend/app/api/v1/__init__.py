"""
柴管家 API v1 路由模块
"""

from fastapi import APIRouter
from app.api.v1 import health

# 创建API路由器
api_router = APIRouter()

# 注册各模块路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])

# 预留其他路由
# api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
# api_router.include_router(channels.router, prefix="/channels", tags=["渠道管理"])
# api_router.include_router(messages.router, prefix="/messages", tags=["消息管理"])