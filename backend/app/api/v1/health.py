"""
柴管家健康检查路由
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
import redis
import pika

from app.core.database import get_database, db_manager
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("api.health")
router = APIRouter()


@router.get("/")
async def health_check():
    """基础健康检查端点"""
    logger.debug("基础健康检查")
    return {
        "status": "healthy", 
        "service": "chaiguanjia-api",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/db")
async def database_health(db: Session = Depends(get_database)):
    """数据库健康检查"""
    logger.debug("数据库健康检查")
    try:
        # 使用数据库管理器进行健康检查
        health_result = db_manager.health_check()
        
        if health_result["status"] == "healthy":
            return {
                "status": "healthy", 
                "database": "connected",
                "message": "数据库连接正常",
                "details": health_result
            }
        else:
            raise HTTPException(
                status_code=503, 
                detail={
                    "status": "unhealthy",
                    "database": "disconnected",
                    "error": health_result.get("error"),
                    "details": health_result
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("数据库健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503, 
            detail=f"数据库连接失败: {str(e)}"
        )


@router.get("/redis")
async def redis_health():
    """Redis健康检查"""
    logger.debug("Redis健康检查")
    try:
        start_time = datetime.now()
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds() * 1000
        
        return {
            "status": "healthy",
            "redis": "connected", 
            "message": "Redis连接正常",
            "response_time_ms": round(response_time, 2),
            "url": settings.REDIS_URL.split('@')[-1] if '@' in settings.REDIS_URL else settings.REDIS_URL
        }
    except Exception as e:
        logger.error("Redis健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"Redis连接失败: {str(e)}"
        )


@router.get("/rabbitmq")
async def rabbitmq_health():
    """RabbitMQ健康检查"""
    logger.debug("RabbitMQ健康检查")
    try:
        start_time = datetime.now()
        connection = pika.BlockingConnection(
            pika.URLParameters(settings.RABBITMQ_URL)
        )
        connection.close()
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds() * 1000
        
        return {
            "status": "healthy",
            "rabbitmq": "connected",
            "message": "RabbitMQ连接正常",
            "response_time_ms": round(response_time, 2),
            "url": settings.RABBITMQ_URL.split('@')[-1] if '@' in settings.RABBITMQ_URL else settings.RABBITMQ_URL
        }
    except Exception as e:
        logger.error("RabbitMQ健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"RabbitMQ连接失败: {str(e)}"
        )


@router.get("/full")
async def full_health_check(db: Session = Depends(get_database)):
    """完整健康检查 - 检查所有依赖服务"""
    logger.info("执行完整健康检查")
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "chaiguanjia-api",
        "version": "1.0.0",
        "checks": {},
        "summary": {
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0
        }
    }
    
    checks = [
        ("database", check_database),
        ("redis", check_redis),
        ("rabbitmq", check_rabbitmq)
    ]
    
    for check_name, check_func in checks:
        health_status["summary"]["total_checks"] += 1
        try:
            result = await check_func()
            health_status["checks"][check_name] = {
                "status": "healthy",
                "details": result
            }
            health_status["summary"]["passed_checks"] += 1
            logger.debug(f"{check_name}健康检查通过")
        except Exception as e:
            health_status["checks"][check_name] = {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
            health_status["summary"]["failed_checks"] += 1
            health_status["status"] = "unhealthy"
            logger.error(f"{check_name}健康检查失败", error=str(e))
    
    # 计算健康评分
    if health_status["summary"]["total_checks"] > 0:
        health_score = (health_status["summary"]["passed_checks"] / 
                       health_status["summary"]["total_checks"]) * 100
        health_status["summary"]["health_score"] = round(health_score, 1)
    
    if health_status["status"] == "unhealthy":
        logger.warning("完整健康检查失败", failed_checks=health_status["summary"]["failed_checks"])
        raise HTTPException(status_code=503, detail=health_status)
    
    logger.info("完整健康检查通过", health_score=health_status["summary"]["health_score"])
    return health_status


async def check_database():
    """检查数据库连接"""
    return db_manager.health_check()


async def check_redis():
    """检查Redis连接"""
    start_time = datetime.now()
    r = redis.from_url(settings.REDIS_URL)
    r.ping()
    end_time = datetime.now()
    
    return {
        "connected": True,
        "response_time_ms": round((end_time - start_time).total_seconds() * 1000, 2)
    }


async def check_rabbitmq():
    """检查RabbitMQ连接"""
    start_time = datetime.now()
    connection = pika.BlockingConnection(
        pika.URLParameters(settings.RABBITMQ_URL)
    )
    connection.close()
    end_time = datetime.now()
    
    return {
        "connected": True,
        "response_time_ms": round((end_time - start_time).total_seconds() * 1000, 2)
    }