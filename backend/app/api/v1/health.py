"""
柴管家健康检查路由
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
import redis
import pika
from app.core.database import get_database
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/")
async def health_check():
    """基础健康检查端点"""
    return {
        "status": "healthy", 
        "service": "chaiguanjia-api",
        "version": "1.0.0"
    }

@router.get("/db")
async def database_health(db: Session = Depends(get_database)):
    """数据库健康检查"""
    try:
        # 执行简单查询验证数据库连接
        result = db.execute(text("SELECT 1 as health_check"))
        row = result.fetchone()
        
        if row and row[0] == 1:
            return {
                "status": "healthy", 
                "database": "connected",
                "message": "数据库连接正常"
            }
        else:
            raise HTTPException(status_code=503, detail="数据库查询异常")
            
    except Exception as e:
        logger.error("数据库健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503, 
            detail=f"数据库连接失败: {str(e)}"
        )

@router.get("/redis")
async def redis_health():
    """Redis健康检查"""
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        return {
            "status": "healthy",
            "redis": "connected", 
            "message": "Redis连接正常"
        }
    except Exception as e:
        logger.error("Redis健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"Redis连接失败: {str(e)}"
        )

@router.get("/rabbitmq")
async def rabbitmq_health():
    """RabbitMQ健康检查"""
    try:
        connection = pika.BlockingConnection(
            pika.URLParameters(settings.RABBITMQ_URL)
        )
        connection.close()
        return {
            "status": "healthy",
            "rabbitmq": "connected",
            "message": "RabbitMQ连接正常"
        }
    except Exception as e:
        logger.error("RabbitMQ健康检查失败", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"RabbitMQ连接失败: {str(e)}"
        )

@router.get("/full")
async def full_health_check(db: Session = Depends(get_database)):
    """完整健康检查 - 检查所有依赖服务"""
    health_status = {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "checks": {}
    }
    
    # 检查数据库
    try:
        db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = "healthy"
    except Exception as e:
        health_status["checks"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # 检查Redis
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        health_status["checks"]["redis"] = "healthy"
    except Exception as e:
        health_status["checks"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # 检查RabbitMQ
    try:
        connection = pika.BlockingConnection(
            pika.URLParameters(settings.RABBITMQ_URL)
        )
        connection.close()
        health_status["checks"]["rabbitmq"] = "healthy"
    except Exception as e:
        health_status["checks"]["rabbitmq"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    if health_status["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status