"""
柴管家日志系统配置模块
"""

import logging
import logging.config
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

import structlog
from structlog.stdlib import LoggerFactory

from app.core.config import settings


def setup_logging() -> None:
    """配置结构化日志系统"""
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志文件路径
    log_file = log_dir / f"chaiguanjia_{datetime.now().strftime('%Y%m%d')}.log"
    error_log_file = log_dir / f"chaiguanjia_error_{datetime.now().strftime('%Y%m%d')}.log"
    
    # 标准库日志配置
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
                "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
            },
            "console": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "console",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json" if settings.LOG_FORMAT == "json" else "console",
                "filename": str(log_file),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json" if settings.LOG_FORMAT == "json" else "console",
                "filename": str(error_log_file),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "": {  # 根日志器
                "handlers": ["console", "file", "error_file"],
                "level": settings.LOG_LEVEL,
                "propagate": False,
            },
            "uvicorn": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "uvicorn.error": {
                "handlers": ["console", "error_file"],
                "level": "ERROR",
                "propagate": False,
            },
            "uvicorn.access": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "handlers": ["file"],
                "level": "INFO" if settings.DATABASE_ECHO else "WARNING",
                "propagate": False,
            },
            "app": {
                "handlers": ["console", "file", "error_file"],
                "level": settings.LOG_LEVEL,
                "propagate": False,
            },
        },
    }
    
    # 应用日志配置
    logging.config.dictConfig(logging_config)
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.dev.ConsoleRenderer() if settings.API_DEBUG else structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.LOG_LEVEL.upper())
        ),
        context_class=dict,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = None) -> structlog.stdlib.BoundLogger:
    """获取结构化日志器
    
    Args:
        name: 日志器名称，默认使用调用模块名
        
    Returns:
        配置好的结构化日志器
    """
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return structlog.get_logger(name)


class LoggingMiddleware:
    """请求日志中间件"""
    
    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware.logging")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 提取请求信息
        request_info = {
            "method": scope["method"],
            "path": scope["path"],
            "query_string": scope.get("query_string", b"").decode(),
            "client": scope.get("client"),
            "headers": dict(scope.get("headers", [])),
        }
        
        start_time = datetime.now()
        
        # 包装send函数以捕获响应状态
        status_code = None
        
        async def wrapped_send(message):
            nonlocal status_code
            if message["type"] == "http.response.start":
                status_code = message["status"]
            await send(message)
        
        try:
            await self.app(scope, receive, wrapped_send)
            
            # 计算处理时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录请求日志
            self.logger.info(
                "HTTP请求处理完成",
                **request_info,
                status_code=status_code,
                duration_seconds=duration,
                timestamp=end_time.isoformat(),
            )
            
        except Exception as e:
            # 记录错误日志
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(
                "HTTP请求处理异常",
                **request_info,
                error=str(e),
                error_type=type(e).__name__,
                duration_seconds=duration,
                timestamp=end_time.isoformat(),
            )
            raise


def log_function_call(func_name: str, **kwargs) -> Dict[str, Any]:
    """记录函数调用的辅助函数
    
    Args:
        func_name: 函数名称
        **kwargs: 函数参数
        
    Returns:
        用于日志记录的上下文字典
    """
    return {
        "function": func_name,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }


def log_api_call(endpoint: str, method: str, **kwargs) -> Dict[str, Any]:
    """记录API调用的辅助函数
    
    Args:
        endpoint: API端点
        method: HTTP方法
        **kwargs: 额外参数
        
    Returns:
        用于日志记录的上下文字典
    """
    return {
        "api_endpoint": endpoint,
        "http_method": method,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }


def log_database_operation(operation: str, table: str, **kwargs) -> Dict[str, Any]:
    """记录数据库操作的辅助函数
    
    Args:
        operation: 操作类型（SELECT, INSERT, UPDATE, DELETE）
        table: 表名
        **kwargs: 额外参数
        
    Returns:
        用于日志记录的上下文字典
    """
    return {
        "db_operation": operation,
        "db_table": table,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }


def log_external_api_call(service: str, endpoint: str, **kwargs) -> Dict[str, Any]:
    """记录外部API调用的辅助函数
    
    Args:
        service: 服务名称
        endpoint: API端点
        **kwargs: 额外参数
        
    Returns:
        用于日志记录的上下文字典
    """
    return {
        "external_service": service,
        "external_endpoint": endpoint,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }