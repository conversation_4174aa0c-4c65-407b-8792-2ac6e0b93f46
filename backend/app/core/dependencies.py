"""
依赖注入模块
"""

from typing import Generator, Optional, Annotated
from fastapi import Depends, HTTPException, status, Header, Query
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_database, SessionLocal
from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.models.user import User

logger = get_logger("core.dependencies")
security = HTTPBearer(auto_error=False)


# ============ 数据库依赖 ============

def get_db() -> Generator[Session, None, None]:
    """获取数据库会话依赖"""
    db = SessionLocal()
    try:
        logger.debug("创建数据库会话")
        yield db
    except Exception as e:
        logger.error("数据库会话异常", error=str(e))
        db.rollback()
        raise
    finally:
        logger.debug("关闭数据库会话")
        db.close()


# ============ 认证依赖 ============

async def get_optional_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """获取可选的认证令牌"""
    if credentials:
        return credentials.credentials
    return None


async def get_required_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """获取必需的认证令牌"""
    if not credentials:
        logger.warning("缺少认证令牌")
        raise AuthenticationError("缺少认证令牌")
    
    return credentials.credentials


async def verify_token(token: str) -> dict:
    """验证JWT令牌"""
    try:
        # TODO: 实现实际的JWT验证逻辑
        # 1. 解析JWT token
        # 2. 验证签名
        # 3. 检查过期时间
        # 4. 检查黑名单
        
        # 模拟验证逻辑（待实现）
        if token == "invalid_token":
            raise AuthenticationError("无效的令牌")
        
        # 模拟返回用户信息
        return {
            "user_id": "user_123",
            "email": "<EMAIL>",
            "role": "user",
            "permissions": ["read", "write"]
        }
        
    except Exception as e:
        logger.error("令牌验证失败", token=token[:10] + "...", error=str(e))
        raise AuthenticationError("令牌验证失败")


async def get_current_user_from_token(
    token: str = Depends(get_required_token),
    db: Session = Depends(get_db)
) -> dict:
    """从令牌获取当前用户信息"""
    logger.debug("从令牌获取用户信息")
    
    try:
        # 验证令牌
        token_data = await verify_token(token)
        user_id = token_data.get("user_id")
        
        if not user_id:
            raise AuthenticationError("令牌中缺少用户信息")
        
        # TODO: 从数据库查询用户信息
        # user = db.query(User).filter(User.id == user_id, User.is_deleted == False).first()
        # if not user:
        #     raise AuthenticationError("用户不存在")
        
        # 模拟用户数据（待实现）
        user_data = {
            "id": user_id,
            "email": token_data.get("email"),
            "role": token_data.get("role", "user"),
            "permissions": token_data.get("permissions", [])
        }
        
        logger.debug("用户信息获取成功", user_id=user_id)
        return user_data
        
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error("获取用户信息失败", error=str(e))
        raise AuthenticationError("获取用户信息失败")


async def get_optional_current_user(
    token: Optional[str] = Depends(get_optional_token),
    db: Session = Depends(get_db)
) -> Optional[dict]:
    """获取可选的当前用户信息"""
    if not token:
        return None
    
    try:
        return await get_current_user_from_token(token, db)
    except AuthenticationError:
        return None


# ============ 权限依赖 ============

def require_permission(permission: str):
    """要求特定权限的依赖工厂"""
    
    async def permission_checker(
        current_user: dict = Depends(get_current_user_from_token)
    ) -> dict:
        """检查用户权限"""
        user_permissions = current_user.get("permissions", [])
        
        if permission not in user_permissions and "admin" not in user_permissions:
            logger.warning(
                "用户权限不足",
                user_id=current_user.get("id"),
                required_permission=permission,
                user_permissions=user_permissions
            )
            raise AuthorizationError(f"需要权限: {permission}")
        
        logger.debug(
            "权限检查通过",
            user_id=current_user.get("id"),
            permission=permission
        )
        return current_user
    
    return permission_checker


def require_role(role: str):
    """要求特定角色的依赖工厂"""
    
    async def role_checker(
        current_user: dict = Depends(get_current_user_from_token)
    ) -> dict:
        """检查用户角色"""
        user_role = current_user.get("role")
        
        if user_role != role and user_role != "admin":
            logger.warning(
                "用户角色不足",
                user_id=current_user.get("id"),
                required_role=role,
                user_role=user_role
            )
            raise AuthorizationError(f"需要角色: {role}")
        
        logger.debug(
            "角色检查通过",
            user_id=current_user.get("id"),
            role=role
        )
        return current_user
    
    return role_checker


# ============ 管理员依赖 ============

async def get_admin_user(
    current_user: dict = Depends(require_role("admin"))
) -> dict:
    """获取管理员用户"""
    return current_user


# ============ 分页依赖 ============

class PaginationParams:
    """分页参数"""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="页码，从1开始"),
        page_size: int = Query(20, ge=1, le=100, description="每页数量，最大100")
    ):
        self.page = page
        self.page_size = page_size
        self.offset = (page - 1) * page_size
        self.limit = page_size


def get_pagination_params(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，最大100")
) -> PaginationParams:
    """获取分页参数"""
    return PaginationParams(page, page_size)


# ============ 请求上下文依赖 ============

async def get_request_id(
    x_request_id: Optional[str] = Header(None, alias="X-Request-ID")
) -> str:
    """获取请求ID"""
    if x_request_id:
        return x_request_id
    
    # 生成新的请求ID
    import uuid
    return str(uuid.uuid4())


async def get_client_info(
    user_agent: Optional[str] = Header(None, alias="User-Agent"),
    x_forwarded_for: Optional[str] = Header(None, alias="X-Forwarded-For"),
    x_real_ip: Optional[str] = Header(None, alias="X-Real-IP")
) -> dict:
    """获取客户端信息"""
    # 确定真实IP
    client_ip = x_real_ip or x_forwarded_for or "unknown"
    if x_forwarded_for and "," in x_forwarded_for:
        client_ip = x_forwarded_for.split(",")[0].strip()
    
    return {
        "user_agent": user_agent,
        "ip_address": client_ip,
        "forwarded_for": x_forwarded_for,
        "real_ip": x_real_ip
    }


# ============ 业务依赖 ============

async def get_user_channel_access(
    channel_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: Session = Depends(get_db)
) -> dict:
    """验证用户对渠道的访问权限"""
    try:
        # TODO: 实现实际的渠道权限检查
        # 1. 查询渠道是否存在
        # 2. 检查渠道是否属于当前用户
        # 3. 检查渠道状态
        
        # 模拟权限检查（待实现）
        if channel_id == "unauthorized_channel":
            raise AuthorizationError("无权访问此渠道")
        
        logger.debug("渠道访问权限验证通过", channel_id=channel_id, user_id=current_user["id"])
        return {
            "channel_id": channel_id,
            "user_id": current_user["id"],
            "access_granted": True
        }
        
    except AuthorizationError:
        raise
    except Exception as e:
        logger.error("渠道权限检查失败", channel_id=channel_id, error=str(e))
        raise AuthorizationError("渠道权限检查失败")


async def get_user_conversation_access(
    conversation_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: Session = Depends(get_db)
) -> dict:
    """验证用户对会话的访问权限"""
    try:
        # TODO: 实现实际的会话权限检查
        # 1. 查询会话是否存在
        # 2. 检查会话是否属于当前用户的渠道
        # 3. 检查会话状态
        
        # 模拟权限检查（待实现）
        if conversation_id == "unauthorized_conversation":
            raise AuthorizationError("无权访问此会话")
        
        logger.debug("会话访问权限验证通过", conversation_id=conversation_id, user_id=current_user["id"])
        return {
            "conversation_id": conversation_id,
            "user_id": current_user["id"],
            "access_granted": True
        }
        
    except AuthorizationError:
        raise
    except Exception as e:
        logger.error("会话权限检查失败", conversation_id=conversation_id, error=str(e))
        raise AuthorizationError("会话权限检查失败")


# ============ 速率限制依赖 ============

class RateLimitInfo:
    """速率限制信息"""
    
    def __init__(self, key: str, limit: int, window: int):
        self.key = key
        self.limit = limit
        self.window = window
        self.remaining = limit
        self.reset_time = datetime.utcnow()


async def rate_limit_user(
    limit: int = 60,  # 每分钟60次
    window: int = 60,  # 60秒窗口
    current_user: dict = Depends(get_current_user_from_token)
) -> RateLimitInfo:
    """用户级别的速率限制"""
    user_id = current_user["id"]
    rate_limit_key = f"rate_limit:user:{user_id}"
    
    # TODO: 实现实际的速率限制逻辑
    # 1. 使用Redis记录请求次数
    # 2. 检查是否超过限制
    # 3. 更新请求计数
    
    # 模拟速率限制检查（待实现）
    logger.debug("用户速率限制检查", user_id=user_id, limit=limit)
    
    return RateLimitInfo(rate_limit_key, limit, window)


# ============ 类型别名 ============

# 常用依赖的类型别名
CurrentUser = Annotated[dict, Depends(get_current_user_from_token)]
OptionalCurrentUser = Annotated[Optional[dict], Depends(get_optional_current_user)]
AdminUser = Annotated[dict, Depends(get_admin_user)]
DatabaseSession = Annotated[Session, Depends(get_db)]
PaginationDeps = Annotated[PaginationParams, Depends(get_pagination_params)]
RequestId = Annotated[str, Depends(get_request_id)]
ClientInfo = Annotated[dict, Depends(get_client_info)]