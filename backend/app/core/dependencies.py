"""
柴管家依赖注入模块
"""

from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from app.core.database import SessionLocal

# 安全相关
security = HTTPBearer()

def get_db() -> Generator:
    """获取数据库会话依赖"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

async def get_current_user(token: str = Depends(security)):
    """获取当前用户（预留功能）"""
    # TODO: 实现JWT token验证
    # 目前返回测试用户
    return {
        "user_id": "test_user", 
        "username": "test",
        "email": "<EMAIL>"
    }

def require_admin(current_user: dict = Depends(get_current_user)):
    """要求管理员权限（预留功能）"""
    # TODO: 实现权限验证
    return current_user