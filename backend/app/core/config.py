"""
柴管家应用配置模块
统一管理所有配置项
"""

from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
import os

class Settings(BaseSettings):
    """应用配置类"""
    
    # API配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 7000
    API_DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-change-in-production"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_dev"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # RabbitMQ配置
    RABBITMQ_URL: str = "amqp://chaiguanjia:password@localhost:5672/"
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # 安全配置
    JWT_SECRET_KEY: str = "your-jwt-secret-key-here"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_PATH: str = "./uploads"
    
    # AI配置（预留）
    OPENAI_API_KEY: Optional[str] = None
    AI_MODEL_NAME: str = "gpt-3.5-turbo"
    
    # 环境配置
    ENVIRONMENT: str = "development"
    TESTING: bool = False
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v):
        """验证数据库URL格式"""
        if not v.startswith(("postgresql://", "postgresql+asyncpg://")):
            raise ValueError("DATABASE_URL必须是PostgreSQL连接字符串")
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        """解析允许的主机列表"""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.TESTING or self.ENVIRONMENT.lower() == "testing"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()