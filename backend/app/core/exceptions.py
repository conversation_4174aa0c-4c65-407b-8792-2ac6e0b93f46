"""
柴管家自定义异常类
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class ChaiGuanJiaException(Exception):
    """柴管家基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ChaiGuanJiaHTTPException(HTTPException):
    """柴管家HTTP异常类"""
    
    def __init__(
        self,
        status_code: int,
        message: str,
        error_code: str = "HTTP_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.error_code = error_code
        self.details = details or {}
        super().__init__(status_code=status_code, detail=message)


class DatabaseError(ChaiGuanJiaException):
    """数据库操作异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
        )


class ValidationError(ChaiGuanJiaException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if field:
            details["field"] = field
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class AuthenticationError(ChaiGuanJiaHTTPException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
        )


class AuthorizationError(ChaiGuanJiaHTTPException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details,
        )


class NotFoundError(ChaiGuanJiaHTTPException):
    """资源未找到异常"""
    
    def __init__(self, resource: str, resource_id: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["resource"] = resource
        if resource_id:
            details["resource_id"] = resource_id
        
        message = f"{resource}未找到"
        if resource_id:
            message += f": {resource_id}"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            message=message,
            error_code="NOT_FOUND_ERROR",
            details=details,
        )


class ConflictError(ChaiGuanJiaHTTPException):
    """资源冲突异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            message=message,
            error_code="CONFLICT_ERROR",
            details=details,
        )


class RateLimitError(ChaiGuanJiaHTTPException):
    """速率限制异常"""
    
    def __init__(self, message: str = "请求频率过高", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
        )


class ExternalServiceError(ChaiGuanJiaException):
    """外部服务异常"""
    
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["service"] = service
        super().__init__(
            message=f"{service}服务异常: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
        )


class ChannelError(ChaiGuanJiaException):
    """渠道相关异常"""
    
    def __init__(self, channel: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["channel"] = channel
        super().__init__(
            message=f"渠道[{channel}]异常: {message}",
            error_code="CHANNEL_ERROR",
            details=details,
        )


class MessageError(ChaiGuanJiaException):
    """消息处理异常"""
    
    def __init__(self, message_type: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["message_type"] = message_type
        super().__init__(
            message=f"消息处理异常[{message_type}]: {message}",
            error_code="MESSAGE_ERROR",
            details=details,
        )


class ConfigurationError(ChaiGuanJiaException):
    """配置异常"""
    
    def __init__(self, config_key: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["config_key"] = config_key
        super().__init__(
            message=f"配置异常[{config_key}]: {message}",
            error_code="CONFIGURATION_ERROR",
            details=details,
        )


class AIServiceError(ChaiGuanJiaException):
    """AI服务异常"""
    
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["ai_service"] = service
        super().__init__(
            message=f"AI服务异常[{service}]: {message}",
            error_code="AI_SERVICE_ERROR",
            details=details,
        )


class BusinessLogicError(ChaiGuanJiaException):
    """业务逻辑异常"""
    
    def __init__(self, business_rule: str, message: str, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["business_rule"] = business_rule
        super().__init__(
            message=f"业务规则违反[{business_rule}]: {message}",
            error_code="BUSINESS_LOGIC_ERROR",
            details=details,
        )