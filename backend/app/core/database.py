"""
柴管家数据库连接模块
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator, Generator

from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool, QueuePool
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings
from app.core.logging import get_logger


logger = get_logger("core.database")


# 根据数据库类型选择连接池
if "sqlite" in settings.DATABASE_URL:
    poolclass = StaticPool
    connect_args = {"check_same_thread": False}
    echo = settings.DATABASE_ECHO
else:
    poolclass = QueuePool
    connect_args = {}
    echo = settings.DATABASE_ECHO


# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=echo,
    poolclass=poolclass,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=5,
    max_overflow=10,
    connect_args=connect_args,
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()


def get_database() -> Generator[Session, None, None]:
    """获取数据库会话依赖"""
    db = SessionLocal()
    try:
        logger.debug("数据库会话创建")
        yield db
    except SQLAlchemyError as e:
        logger.error("数据库会话异常", error=str(e))
        db.rollback()
        raise
    except Exception as e:
        logger.error("数据库会话未知异常", error=str(e))
        db.rollback()
        raise
    finally:
        logger.debug("数据库会话关闭")
        db.close()


@asynccontextmanager
async def get_database_async() -> AsyncGenerator[Session, None]:
    """异步数据库会话管理器"""
    db = SessionLocal()
    try:
        logger.debug("异步数据库会话创建")
        yield db
    except SQLAlchemyError as e:
        logger.error("异步数据库会话异常", error=str(e))
        await asyncio.to_thread(db.rollback)
        raise
    except Exception as e:
        logger.error("异步数据库会话未知异常", error=str(e))
        await asyncio.to_thread(db.rollback)
        raise
    finally:
        logger.debug("异步数据库会话关闭")
        await asyncio.to_thread(db.close)


def create_tables():
    """创建所有数据表"""
    try:
        logger.info("开始创建数据表")
        Base.metadata.create_all(bind=engine)
        logger.info("数据表创建完成")
    except Exception as e:
        logger.error("创建数据表失败", error=str(e))
        raise


def drop_tables():
    """删除所有数据表"""
    try:
        logger.warning("开始删除数据表")
        Base.metadata.drop_all(bind=engine)
        logger.warning("数据表删除完成")
    except Exception as e:
        logger.error("删除数据表失败", error=str(e))
        raise


def check_database_connection() -> bool:
    """检查数据库连接状态"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.fetchone()
        logger.info("数据库连接检查成功")
        return True
    except Exception as e:
        logger.error("数据库连接检查失败", error=str(e))
        return False


def get_database_info() -> dict:
    """获取数据库信息"""
    try:
        with engine.connect() as conn:
            if "postgresql" in settings.DATABASE_URL:
                result = conn.execute(text("SELECT version()"))
                version = result.fetchone()[0]
                db_type = "PostgreSQL"
            elif "sqlite" in settings.DATABASE_URL:
                result = conn.execute(text("SELECT sqlite_version()"))
                version = result.fetchone()[0]
                db_type = "SQLite"
            else:
                version = "unknown"
                db_type = "unknown"
                
        return {
            "type": db_type,
            "version": version,
            "url": settings.DATABASE_URL.split('@')[-1] if '@' in settings.DATABASE_URL else settings.DATABASE_URL,
            "pool_size": engine.pool.size(),
            "checked_out": engine.pool.checkedout(),
        }
    except Exception as e:
        logger.error("获取数据库信息失败", error=str(e))
        return {
            "type": "unknown",
            "version": "unknown",
            "error": str(e)
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
        self.logger = get_logger("core.database.manager")
    
    def health_check(self) -> dict:
        """数据库健康检查"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            end_time = asyncio.get_event_loop().time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            db_info = get_database_info()
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "database_info": db_info,
                "connection_pool": {
                    "size": self.engine.pool.size(),
                    "checked_out": self.engine.pool.checkedout(),
                    "overflow": self.engine.pool.overflow(),
                }
            }
        except Exception as e:
            self.logger.error("数据库健康检查失败", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    def execute_raw_sql(self, sql: str, params: dict = None) -> list:
        """执行原生SQL查询"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), params or {})
                return [dict(row) for row in result]
        except Exception as e:
            self.logger.error("执行原生SQL失败", sql=sql, error=str(e))
            raise
    
    def get_table_info(self, table_name: str) -> dict:
        """获取表信息"""
        try:
            # 获取表的元数据信息
            inspector = engine.dialect.get_table_info
            # TODO: 实现具体的表信息查询逻辑
            return {"table_name": table_name, "status": "exists"}
        except Exception as e:
            self.logger.error("获取表信息失败", table_name=table_name, error=str(e))
            raise


# 创建数据库管理器实例
db_manager = DatabaseManager()