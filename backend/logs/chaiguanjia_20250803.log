{"asctime": "2025-08-03 18:44:28,580", "name": "app.main", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:44:28.580260Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u6839\u7aef\u70b9\u88ab\u8bbf\u95ee\u001b[0m"}
{"asctime": "2025-08-03 18:44:28,580", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:44:28.580483Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.000286\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:44:28,581", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:44:28,581", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:44:28.581655Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m9e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/health\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:44:28,582", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:44:28,582", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:44:28.582575Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m8.4e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/health/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:44:28,582", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/health/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:44:28,583", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:44:28.583398Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m3.9e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/docs\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:44:28,583", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/docs \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,851", "name": "app.main", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.851116Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u6839\u7aef\u70b9\u88ab\u8bbf\u95ee\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,851", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.851460Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.000407\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,852", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,852", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.852548Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m7.5e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/health\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,852", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,853", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.853370Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m6.3e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/health/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,853", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/health/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,854", "name": "api.channels", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.854320Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u83b7\u53d6\u6e20\u9053\u5217\u8868                        \u001b[0m \u001b[36mpage\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mpage_size\u001b[0m=\u001b[35m20\u001b[0m \u001b[36mplatform\u001b[0m=\u001b[35mNone\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,854", "name": "api.channels", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.854399Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u6e20\u9053\u5217\u8868\u83b7\u53d6\u6210\u529f                      \u001b[0m \u001b[36mtotal\u001b[0m=\u001b[35m2\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,855", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.855329Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.00131\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/channels/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,855", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/channels/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,856", "name": "api.messages", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.856241Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u83b7\u53d6\u4f1a\u8bdd\u5217\u8868                        \u001b[0m \u001b[36mchannel_id\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mpage\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mpage_size\u001b[0m=\u001b[35m20\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,856", "name": "api.messages", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.856319Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u4f1a\u8bdd\u5217\u8868\u83b7\u53d6\u6210\u529f                      \u001b[0m \u001b[36mtotal\u001b[0m=\u001b[35m2\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,856", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.856555Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.000568\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/messages/conversations\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,856", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/messages/conversations \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:45:16,857", "name": "api.messages", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.857397Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u83b7\u53d6\u6d88\u606f\u7edf\u8ba1                        \u001b[0m \u001b[36mdays\u001b[0m=\u001b[35m7\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,857", "name": "api.messages", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.857454Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u6d88\u606f\u7edf\u8ba1\u83b7\u53d6\u6210\u529f                      \u001b[0m \u001b[36mdays\u001b[0m=\u001b[35m7\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,857", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:45:16.857626Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.000432\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/messages/stats\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:45:16,857", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/messages/stats \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:46:15,095", "name": "app.main", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:46:15.095449Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1m\u6839\u7aef\u70b9\u88ab\u8bbf\u95ee\u001b[0m"}
{"asctime": "2025-08-03 18:46:15,096", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:46:15.096106Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m0.000722\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:46:15,096", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:46:15,097", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:46:15.097080Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m7.2e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/health\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:46:15,097", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:46:15,097", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:46:15.097886Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m6e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/api/v1/health/\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:46:15,098", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/api/v1/health/ \"HTTP/1.1 200 OK\""}
{"asctime": "2025-08-03 18:46:15,098", "name": "middleware.logging", "levelname": "INFO", "message": "\u001b[2m2025-08-03T10:46:15.098629Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP\u8bf7\u6c42\u5904\u7406\u5b8c\u6210                    \u001b[0m \u001b[36mclient\u001b[0m=\u001b[35m('testclient', 50000)\u001b[0m \u001b[36mduration_seconds\u001b[0m=\u001b[35m3.6e-05\u001b[0m \u001b[36mheaders\u001b[0m=\u001b[35m{b'host': b'testserver', b'accept': b'*/*', b'accept-encoding': b'gzip, deflate', b'connection': b'keep-alive', b'user-agent': b'testclient'}\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/docs\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mstatus_code\u001b[0m=\u001b[35m200\u001b[0m"}
{"asctime": "2025-08-03 18:46:15,098", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: GET http://testserver/docs \"HTTP/1.1 200 OK\""}
