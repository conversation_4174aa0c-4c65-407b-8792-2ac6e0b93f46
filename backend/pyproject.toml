[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "chaiguanjia"
version = "1.0.0"
description = "柴管家：多平台聚合智能客服系统"
authors = [{name = "柴管家团队", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Framework :: FastAPI",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--disable-warnings",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml:coverage.xml",
]
markers = [
    "unit: 单元测试",
    "integration: 集成测试", 
    "bdd: BDD测试",
    "slow: 慢速测试",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/alembic/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
  | alembic
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_glob = ["*/migrations/*", "*/alembic/*"]

[tool.flake8]
max-line-length = 88
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    "alembic",
    ".venv",
    "venv",
]
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "migrations/",
    "alembic/",
]

[[tool.mypy.overrides]]
module = [
    "sqlalchemy.*",
    "alembic.*",
    "pika.*",
    "redis.*",
    "celery.*",
    "openai.*",
    "chromadb.*",
]
ignore_missing_imports = true

[tool.bandit]
exclude_dirs = ["tests", "migrations", "alembic"]
skips = ["B101", "B601"]