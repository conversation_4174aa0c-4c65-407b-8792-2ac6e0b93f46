# 柴管家第一阶段开发方案（Sprint 0）

## 文档信息

| 项目名称 | 柴管家：多平台聚合智能客服系统 |
|----------|----------------------------|
| **阶段版本** | Sprint 0 - 基础设施搭建 |
| **目标用户** | 独立全栈开发者 |
| **创建日期** | 2025-08-03 |
| **预计工期** | 5个工作日 |

## 概述

Sprint 0是项目的基础设施搭建阶段，重点建立完整的开发环境、CI/CD流程和技术框架。本阶段的目标是为后续的业务功能开发奠定坚实的技术基础。

**Sprint 0核心价值**：
- 建立标准化的开发环境和工具链
- 验证技术架构的可行性
- 建立质量保证体系
- 为团队协作提供基础设施

## 技术架构设计

### 核心技术栈确认

```mermaid
graph TB
    subgraph "开发环境技术栈"
        Docker[Docker & Docker Compose<br/>容器化开发环境]
        Python[Python 3.11+<br/>主要开发语言]
        FastAPI[FastAPI 0.104+<br/>API框架]
        PostgreSQL[PostgreSQL 15+<br/>主数据库]
        RabbitMQ[RabbitMQ 3.12+<br/>消息队列]
        Redis[Redis 7.0+<br/>缓存数据库]
    end
    
    subgraph "开发工具链"
        Pytest[pytest + pytest-bdd<br/>测试框架]
        Alembic[Alembic<br/>数据库迁移]
        Black[Black + isort + flake8<br/>代码格式化]
        GitHub[GitHub Actions<br/>CI/CD流程]
    end
    
    subgraph "IDE和调试"
        VSCode[VS Code<br/>推荐IDE]
        Debugger[Python Debugger<br/>断点调试]
        APIDoc[Swagger UI<br/>API文档]
        DBAdmin[pgAdmin<br/>数据库管理]
    end
    
    Docker --> Python
    Python --> FastAPI
    FastAPI --> PostgreSQL
    FastAPI --> RabbitMQ
    FastAPI --> Redis
    
    Pytest --> FastAPI
    Alembic --> PostgreSQL
    Black --> Python
    GitHub --> Docker
    
    style Docker fill:#e1f5fe
    style FastAPI fill:#e8f5e8
    style Pytest fill:#fff3e0
    style GitHub fill:#f3e5f5
```

**技术选型说明**：
- **Docker环境**：确保开发环境一致性，支持一键启动
- **FastAPI**：高性能异步框架，自动生成API文档
- **PostgreSQL**：企业级关系数据库，支持JSONB
- **RabbitMQ**：可靠的消息队列，支持事件驱动架构
- **pytest-bdd**：支持BDD测试方法论
- **GitHub Actions**：免费的CI/CD服务

### 项目目录结构设计

```mermaid
graph TB
    Root[chaiguanjia_cc_8.3/]
    
    Root --> Docker[docker-compose.yml<br/>开发环境配置]
    Root --> GitHub[.github/<br/>CI/CD配置]
    Root --> Backend[backend/<br/>后端应用]
    Root --> Connectors[connectors/<br/>连接器模块]
    Root --> Tests[tests/<br/>测试代码]
    Root --> Features[features/<br/>BDD规范]
    Root --> Verification[verification_ui/<br/>验证界面]
    Root --> Scripts[scripts/<br/>工具脚本]
    Root --> Docs[docs/<br/>技术文档]
    
    Backend --> FastAPIApp[app/<br/>FastAPI应用]
    Backend --> Models[models/<br/>数据模型]
    Backend --> Migrations[alembic/<br/>数据库迁移]
    Backend --> Requirements[requirements.txt<br/>Python依赖]
    
    Tests --> Unit[unit/<br/>单元测试]
    Tests --> Integration[integration/<br/>集成测试]
    Tests --> BDDTests[bdd/<br/>BDD测试实现]
    Tests --> Fixtures[fixtures/<br/>测试数据]
    
    GitHub --> Workflows[workflows/<br/>CI/CD工作流]
    GitHub --> Templates[templates/<br/>Issue和PR模板]
    
    style Root fill:#e3f2fd
    style Backend fill:#f3e5f5
    style Tests fill:#e8f5e8
    style GitHub fill:#fff3e0
```

## Sprint 0开发任务清单

### 任务1：项目结构初始化（1天）

**目标**：建立完整的项目目录结构和基础配置文件

#### 1.1 创建项目根目录结构
```bash
# 执行命令序列
mkdir -p chaiguanjia_cc_8.3/{backend/{app/{core,models,schemas,api/v1,services,utils,workers},alembic/versions},connectors/{base,xianyu,wechat},features/{epic01_channel_management,epic02_unified_workspace,epic03_ai_copilot,epic04_ai_hosting},tests/{unit/{test_models,test_services,test_api},integration,bdd,fixtures,e2e},docs/{api,deployment,development,architecture,bdd},verification_ui/{static/{css,js,images},templates},scripts,.github/{workflows,ISSUE_TEMPLATE}}

# 创建必要的__init__.py文件
find chaiguanjia_cc_8.3 -type d -name "*.py" -o -name "app" -o -name "connectors" -o -name "tests" | xargs -I {} touch {}/__init__.py
```

#### 1.2 创建基础配置文件
**文件清单**：
- `docker-compose.yml` - 开发环境容器编排
- `.env.example` - 环境变量示例
- `.gitignore` - Git忽略规则
- `README.md` - 项目主文档
- `backend/requirements.txt` - Python依赖清单
- `backend/pyproject.toml` - Python项目配置
- `backend/Dockerfile` - 后端镜像构建文件

**验证标准**：
- [ ] 目录结构与设计图一致
- [ ] 所有Python包都有__init__.py文件
- [ ] 配置文件格式正确，无语法错误
- [ ] Git仓库初始化完成

#### 1.3 环境变量配置
创建`.env.example`文件，包含所有必要的环境变量：

```bash
# 数据库配置
DATABASE_URL=postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=chaiguanjia_dev
DATABASE_USER=chaiguanjia
DATABASE_PASSWORD=password

# Redis配置
REDIS_URL=redis://localhost:6379/0

# RabbitMQ配置
RABBITMQ_URL=amqp://chaiguanjia:password@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=chaiguanjia
RABBITMQ_PASSWORD=password

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
SECRET_KEY=your-secret-key-here

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# AI服务配置（预留）
OPENAI_API_KEY=your-openai-key
AI_MODEL_NAME=gpt-3.5-turbo
```

### 任务2：Docker开发环境搭建（1天）

**目标**：建立可一键启动的容器化开发环境

#### 2.1 编写docker-compose.yml
```yaml
version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./features:/app/features
      - ./tests:/app/tests
    environment:
      - DATABASE_URL=*****************************************/chaiguanjia_dev
      - REDIS_URL=redis://redis:6379/0
      - RABBITMQ_URL=amqp://chaiguanjia:password@rabbitmq:5672/
    depends_on:
      - db
      - redis
      - rabbitmq
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # PostgreSQL数据库
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: chaiguanjia_dev
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia
      RABBITMQ_DEFAULT_PASS: password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # 验证界面服务
  verification:
    build:
      context: ./verification_ui
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./verification_ui:/app
    environment:
      - BACKEND_API_URL=http://backend:8000
    depends_on:
      - backend

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
```

#### 2.2 编写backend/Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2.3 验证Docker环境
**验证步骤**：
```bash
# 1. 构建并启动所有服务
docker-compose up --build

# 2. 验证服务状态
docker-compose ps

# 3. 验证数据库连接
docker-compose exec backend python -c "
from app.core.database import engine
with engine.connect() as conn:
    result = conn.execute('SELECT version()')
    print(result.fetchone())
"

# 4. 验证API服务
curl http://localhost:8000/docs

# 5. 验证RabbitMQ管理界面
# 访问 http://localhost:15672 (chaiguanjia/password)
```

**完成标准**：
- [ ] 所有服务成功启动（backend, db, redis, rabbitmq）
- [ ] API文档可访问 http://localhost:8000/docs
- [ ] 数据库连接正常
- [ ] RabbitMQ管理界面可访问
- [ ] 热重载功能正常工作

### 任务3：FastAPI基础框架搭建（1天）

**目标**：建立FastAPI应用的基础架构和核心模块

#### 3.1 创建FastAPI应用入口
**文件**: `backend/app/main.py`
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1 import api_router

# 设置日志
setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title="柴管家 API",
    description="多平台聚合智能客服系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    return {"message": "柴管家 API v1.0.0", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}
```

#### 3.2 创建核心配置模块
**文件**: `backend/app/core/config.py`
```python
from typing import List, Optional
from pydantic import BaseSettings, validator
import os

class Settings(BaseSettings):
    # API配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-change-in-production"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_dev"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # RabbitMQ配置
    RABBITMQ_URL: str = "amqp://chaiguanjia:password@localhost:5672/"
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # AI配置（预留）
    OPENAI_API_KEY: Optional[str] = None
    AI_MODEL_NAME: str = "gpt-3.5-turbo"
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v):
        if not v.startswith(("postgresql://", "postgresql+asyncpg://")):
            raise ValueError("DATABASE_URL必须是PostgreSQL连接字符串")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

#### 3.3 创建数据库连接模块
**文件**: `backend/app/core/database.py`
```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {},
)

# 创建会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

def get_database():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

#### 3.4 创建API路由模块
**文件**: `backend/app/api/v1/__init__.py`
```python
from fastapi import APIRouter

from app.api.v1 import auth, channels, messages, health

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(channels.router, prefix="/channels", tags=["渠道管理"])
api_router.include_router(messages.router, prefix="/messages", tags=["消息管理"])
```

**文件**: `backend/app/api/v1/health.py`
```python
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_database

router = APIRouter()

@router.get("/")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "chaiguanjia-api"}

@router.get("/db")
async def database_health(db: Session = Depends(get_database)):
    """数据库健康检查"""
    try:
        # 执行简单查询验证数据库连接
        db.execute("SELECT 1")
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}
```

#### 3.5 创建基础依赖模块
**文件**: `backend/app/core/dependencies.py`
```python
from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session

from app.core.database import get_database

# 安全相关
security = HTTPBearer()

def get_db() -> Generator:
    """获取数据库会话依赖"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

async def get_current_user(token: str = Depends(security)):
    """获取当前用户（预留）"""
    # TODO: 实现JWT token验证
    return {"user_id": "test_user", "username": "test"}
```

**验证标准**：
- [ ] FastAPI应用成功启动
- [ ] API文档可访问并显示所有端点
- [ ] 健康检查端点返回正确状态
- [ ] 数据库连接正常
- [ ] 所有模块可正常导入

### 任务4：测试框架搭建（1天）

**目标**：建立完整的测试基础设施，支持单元测试、集成测试和BDD测试

#### 4.1 配置pytest测试框架
**文件**: `backend/pyproject.toml`
```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--disable-warnings",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
]
markers = [
    "unit: 单元测试",
    "integration: 集成测试", 
    "bdd: BDD测试",
    "slow: 慢速测试",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
```

#### 4.2 创建测试配置文件
**文件**: `tests/conftest.py`
```python
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_database, Base
from app.core.config import settings

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def db_engine():
    """创建测试数据库引擎"""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(db_engine):
    """创建测试数据库会话"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture(scope="function")
def client(db_session):
    """创建测试客户端"""
    def override_get_database():
        yield db_session
    
    app.dependency_overrides[get_database] = override_get_database
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()

@pytest.fixture
def sample_user():
    """样例用户数据"""
    return {
        "email": "<EMAIL>",
        "full_name": "测试用户",
        "password": "testpassword123"
    }

@pytest.fixture
def sample_channel():
    """样例渠道数据"""
    return {
        "platform": "xianyu",
        "alias": "闲鱼测试账号",
        "credentials": {"token": "test_token_123"}
    }
```

#### 4.3 编写示例单元测试
**文件**: `tests/unit/test_api/test_health.py`
```python
import pytest
from fastapi.testclient import TestClient

def test_health_check(client: TestClient):
    """测试健康检查端点"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "chaiguanjia-api"

def test_database_health(client: TestClient):
    """测试数据库健康检查"""
    response = client.get("/api/v1/health/db")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"

def test_root_endpoint(client: TestClient):
    """测试根端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "status" in data
    assert data["status"] == "running"
```

#### 4.4 配置BDD测试框架
**文件**: `backend/requirements.txt` (添加BDD依赖)
```
pytest-bdd>=7.0.0
```

**文件**: `features/epic01_channel_management/channel_health.feature`
```gherkin
# language: zh-CN
功能: 系统健康检查
  作为系统管理员
  我需要能够检查系统的健康状态
  以便确保系统正常运行

  场景: 系统健康检查成功
    当 我请求系统健康检查接口
    那么 系统应该返回健康状态
    并且 响应状态码应该是 200
    并且 响应应该包含服务名称

  场景: 数据库健康检查成功
    当 我请求数据库健康检查接口
    那么 系统应该返回数据库连接状态
    并且 响应状态码应该是 200
    并且 数据库状态应该是连接的
```

**文件**: `tests/bdd/test_system_health.py`
```python
import pytest
from pytest_bdd import scenarios, given, when, then, parsers
from fastapi.testclient import TestClient

# 加载BDD场景
scenarios('../features/epic01_channel_management/channel_health.feature')

@when('我请求系统健康检查接口')
def request_health_check(client):
    client.response = client.get("/api/v1/health/")

@when('我请求数据库健康检查接口')
def request_db_health_check(client):
    client.response = client.get("/api/v1/health/db")

@then('系统应该返回健康状态')
def check_health_response(client):
    data = client.response.json()
    assert data["status"] == "healthy"

@then(parsers.parse('响应状态码应该是 {status_code:d}'))
def check_status_code(client, status_code):
    assert client.response.status_code == status_code

@then('响应应该包含服务名称')
def check_service_name(client):
    data = client.response.json()
    assert "service" in data
    assert data["service"] == "chaiguanjia-api"

@then('系统应该返回数据库连接状态')
def check_db_response(client):
    data = client.response.json()
    assert "database" in data

@then('数据库状态应该是连接的')
def check_db_connected(client):
    data = client.response.json()
    assert data["database"] == "connected"
```

#### 4.5 测试运行脚本
**文件**: `scripts/run_tests.sh`
```bash
#!/bin/bash

echo "运行柴管家测试套件..."

# 设置测试环境变量
export TESTING=true
export DATABASE_URL="sqlite:///./test.db"

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 运行代码质量检查
echo "1. 运行代码格式检查..."
black --check backend/app backend/tests
isort --check-only backend/app backend/tests
flake8 backend/app backend/tests

# 运行单元测试
echo "2. 运行单元测试..."
pytest tests/unit -v --cov=app --cov-report=term-missing

# 运行集成测试
echo "3. 运行集成测试..."
pytest tests/integration -v

# 运行BDD测试
echo "4. 运行BDD测试..."
pytest tests/bdd -v

echo "测试完成！"
```

**验证标准**：
- [ ] 所有测试配置文件创建完成
- [ ] 示例单元测试可以运行并通过
- [ ] BDD测试框架配置正确
- [ ] 测试覆盖率报告生成正常
- [ ] 代码质量检查工具配置正确

### 任务5：CI/CD流程配置（1天）

**目标**：建立自动化的持续集成和持续部署流程

#### 5.1 GitHub Actions工作流配置

**文件**: `.github/workflows/ci.yml`
```yaml
name: 持续集成

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: chaiguanjia_test
          POSTGRES_USER: chaiguanjia
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
          
      rabbitmq:
        image: rabbitmq:3.12-management
        env:
          RABBITMQ_DEFAULT_USER: chaiguanjia
          RABBITMQ_DEFAULT_PASS: password
        options: >-
          --health-cmd "rabbitmqctl status"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5672:5672

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 缓存pip依赖
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt

    - name: 代码格式检查
      run: |
        black --check backend/app
        isort --check-only backend/app
        flake8 backend/app

    - name: 单元测试
      env:
        DATABASE_URL: postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_test
        REDIS_URL: redis://localhost:6379/0
        RABBITMQ_URL: amqp://chaiguanjia:password@localhost:5672/
      run: |
        cd backend
        pytest tests/unit -v --cov=app --cov-report=xml

    - name: 集成测试
      env:
        DATABASE_URL: postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_test
        REDIS_URL: redis://localhost:6379/0
        RABBITMQ_URL: amqp://chaiguanjia:password@localhost:5672/
      run: |
        cd backend
        pytest tests/integration -v

    - name: BDD测试
      env:
        DATABASE_URL: postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_test
        REDIS_URL: redis://localhost:6379/0
        RABBITMQ_URL: amqp://chaiguanjia:password@localhost:5672/
      run: |
        cd backend
        pytest tests/bdd -v

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 安全扫描
      run: |
        pip install bandit safety
        bandit -r backend/app -f json -o bandit-report.json
        safety check --json --output safety-report.json || true
        
    - name: 上传安全报告
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v2
      
    - name: 构建Docker镜像
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        push: false
        tags: chaiguanjia/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

#### 5.2 代码质量检查配置

**文件**: `.github/workflows/quality.yml`
```yaml
name: 代码质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 安装质量检查工具
      run: |
        pip install black isort flake8 mypy pylint
        
    - name: Black代码格式检查
      run: black --check --diff backend/app backend/tests
      
    - name: isort导入排序检查
      run: isort --check-only --diff backend/app backend/tests
      
    - name: flake8代码风格检查
      run: flake8 backend/app backend/tests
      
    - name: mypy类型检查
      run: mypy backend/app --ignore-missing-imports
      
    - name: pylint代码质量评分
      run: pylint backend/app --output-format=json > pylint-report.json || true
      
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports
        path: pylint-report.json
```

#### 5.3 Issue和PR模板

**文件**: `.github/ISSUE_TEMPLATE/epic.yml`
```yaml
name: 史诗 (Epic)
description: 创建新的史诗级功能
title: "[Epic] "
labels: ["epic", "planning"]
body:
  - type: input
    id: epic-name
    attributes:
      label: 史诗名称
      description: 简洁描述史诗的核心功能
      placeholder: 例如：核心渠道管理
    validations:
      required: true
      
  - type: textarea
    id: business-value
    attributes:
      label: 业务价值
      description: 这个史诗为用户带来什么价值？
      placeholder: 描述史诗解决的核心问题和带来的业务价值
    validations:
      required: true
      
  - type: textarea
    id: acceptance-criteria
    attributes:
      label: 验收标准
      description: 史诗完成的标准是什么？
      placeholder: |
        - [ ] 标准1
        - [ ] 标准2
        - [ ] 标准3
    validations:
      required: true
      
  - type: textarea
    id: user-stories
    attributes:
      label: 包含的用户故事
      description: 列出这个史诗包含的用户故事
      placeholder: |
        - User Story 1: 
        - User Story 2: 
        - User Story 3: 
    validations:
      required: true
      
  - type: input
    id: estimate
    attributes:
      label: 工期估算
      description: 预计完成时间（天）
      placeholder: 例如：14天
    validations:
      required: true
```

**文件**: `.github/ISSUE_TEMPLATE/user_story.yml`
```yaml
name: 用户故事 (User Story)
description: 创建新的用户故事
title: "[Story] "
labels: ["story", "backlog"]
body:
  - type: input
    id: story-title
    attributes:
      label: 用户故事标题
      description: 使用 "As a... I want... So that..." 格式
      placeholder: 作为 IP运营者，我希望 能够添加渠道实例，以便 管理多个平台账号
    validations:
      required: true
      
  - type: dropdown
    id: epic
    attributes:
      label: 所属史诗
      description: 这个用户故事属于哪个史诗？
      options:
        - Epic 1: 核心渠道管理
        - Epic 2: 统一消息工作台
        - Epic 3: AI副驾与知识库
        - Epic 4: AI托管与人工接管
    validations:
      required: true
      
  - type: textarea
    id: description
    attributes:
      label: 详细描述
      description: 详细描述用户故事的功能和场景
    validations:
      required: true
      
  - type: textarea
    id: acceptance-criteria
    attributes:
      label: 验收标准
      description: 功能完成的标准和测试要求
      placeholder: |
        - [ ] 功能标准1
        - [ ] 功能标准2
        - [ ] 测试要求
    validations:
      required: true
      
  - type: textarea
    id: technical-tasks
    attributes:
      label: 技术任务分解
      description: 将用户故事分解为具体的技术任务
      placeholder: |
        - [ ] 数据模型设计
        - [ ] API端点实现
        - [ ] 单元测试编写
        - [ ] 集成测试编写
    validations:
      required: true
      
  - type: input
    id: estimate
    attributes:
      label: 时间估算
      description: 预计完成时间（小时）
      placeholder: 例如：16小时
    validations:
      required: true
```

**文件**: `.github/PULL_REQUEST_TEMPLATE.md`
```markdown
## PR类型
请在适用的选项前打 ✅

- [ ] 🐛 Bug修复
- [ ] ✨ 新功能
- [ ] 📚 文档更新
- [ ] 🎨 代码重构
- [ ] ⚡ 性能优化
- [ ] ✅ 测试相关
- [ ] 🔧 配置变更

## 变更内容

### 概述
简要描述这个PR的变更内容

### 相关Issue
Closes #(issue编号)

### 变更细节
- 变更1：描述
- 变更2：描述
- 变更3：描述

## 测试

### 测试类型
- [ ] 单元测试
- [ ] 集成测试
- [ ] BDD测试
- [ ] 手动测试

### 测试结果
- [ ] 所有现有测试通过
- [ ] 新增测试覆盖新功能
- [ ] 测试覆盖率 ≥ 85%

## 质量检查

- [ ] 代码格式检查通过 (black, isort, flake8)
- [ ] 类型检查通过 (mypy)
- [ ] 安全扫描通过 (bandit)
- [ ] 代码审查完成

## 部署说明

### 环境变量变更
如有新增或修改的环境变量，请在此说明

### 数据库迁移
- [ ] 无需迁移
- [ ] 需要运行迁移：`alembic upgrade head`

### 依赖变更
- [ ] 无新增依赖
- [ ] 新增依赖：请列出

## 验证步骤

1. 检出此分支
2. 运行 `docker-compose up --build`
3. 访问 `http://localhost:8000/docs` 验证API
4. 运行测试套件：`./scripts/run_tests.sh`

## 截图/GIF
如果有UI变更，请提供截图或GIF

## 审查清单

### 代码质量
- [ ] 代码可读性良好
- [ ] 错误处理完善
- [ ] 日志记录适当
- [ ] 性能考虑充分

### 文档
- [ ] API文档已更新
- [ ] README已更新（如适用）
- [ ] 代码注释充分

### 安全
- [ ] 无敏感信息泄露
- [ ] 输入验证充分
- [ ] 权限检查正确
```

**验证标准**：
- [ ] CI/CD工作流配置正确
- [ ] 所有质量门禁检查通过
- [ ] Issue和PR模板创建完成
- [ ] 自动化测试运行正常
- [ ] 代码覆盖率报告生成

## 质量验证与完成标准

### Sprint 0完成检查清单

#### 基础设施验证
- [ ] **项目结构**：目录结构完整，符合设计规范
- [ ] **Docker环境**：所有服务正常启动，容器间通信正常
- [ ] **数据库连接**：PostgreSQL连接正常，可执行基础查询
- [ ] **消息队列**：RabbitMQ服务正常，管理界面可访问
- [ ] **缓存服务**：Redis服务正常运行

#### API框架验证
- [ ] **FastAPI应用**：应用成功启动，基础端点响应正常
- [ ] **API文档**：Swagger UI可访问，显示所有已实现端点
- [ ] **健康检查**：所有健康检查端点返回正确状态
- [ ] **配置管理**：环境变量正确加载，配置验证正常
- [ ] **日志系统**：日志格式正确，记录完整

#### 测试框架验证
- [ ] **单元测试**：pytest配置正确，示例测试通过
- [ ] **集成测试**：数据库集成测试正常
- [ ] **BDD测试**：pytest-bdd配置正确，示例场景执行成功
- [ ] **覆盖率报告**：测试覆盖率统计正常，报告生成正确
- [ ] **测试数据**：测试固件配置正确，数据隔离正常

#### CI/CD流程验证
- [ ] **自动化测试**：GitHub Actions工作流正常执行
- [ ] **代码质量检查**：所有质量门禁通过
- [ ] **安全扫描**：安全扫描正常执行，无严重漏洞
- [ ] **构建验证**：Docker镜像构建成功
- [ ] **模板配置**：Issue和PR模板正常工作

### 性能验证标准

#### 启动性能
- [ ] **容器启动**：所有服务在60秒内启动完成
- [ ] **API响应**：健康检查端点响应时间 < 200ms
- [ ] **数据库连接**：数据库连接建立时间 < 5秒

#### 测试性能
- [ ] **单元测试**：完整单元测试套件执行时间 < 30秒
- [ ] **集成测试**：集成测试执行时间 < 60秒
- [ ] **BDD测试**：BDD测试执行时间 < 60秒

### 文档验证标准

- [ ] **README.md**：项目说明完整，包含快速启动指南
- [ ] **API文档**：所有端点都有完整的文档说明
- [ ] **环境配置**：环境变量说明完整，示例配置正确
- [ ] **开发指南**：包含本地开发环境搭建步骤

## 下一步行动计划

### 立即执行（Sprint 0结束后）
1. **代码审查**：组织团队进行Sprint 0成果的代码审查
2. **环境测试**：在不同操作系统上验证开发环境的一致性
3. **性能基准**：建立基础性能指标，为后续优化提供基准
4. **团队培训**：确保所有开发人员熟悉工具链和开发流程

### Sprint 1准备工作
1. **Epic 1规划**：详细规划渠道管理功能的用户故事
2. **数据模型设计**：设计渠道相关的数据库表结构
3. **API规范制定**：制定渠道管理API的详细规范
4. **BDD场景编写**：编写Epic 1的完整BDD测试场景

### 风险预防措施
1. **技术风险**：为第三方API集成准备备选方案
2. **进度风险**：设置里程碑检查点，及时发现进度偏差
3. **质量风险**：建立代码审查机制，确保代码质量
4. **环境风险**：准备环境故障的快速恢复方案

---

**Sprint 0总结**：
- **预计工期**：5个工作日
- **核心交付**：完整的开发基础设施和工具链
- **成功标准**：所有验证标准通过，为Epic 1开发做好准备
- **关键成果**：标准化的开发环境、自动化的CI/CD流程、完整的测试框架

通过Sprint 0的实施，项目将建立起坚实的技术基础，为后续的业务功能开发提供强有力的支撑。