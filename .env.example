# 数据库配置
DATABASE_URL=postgresql://chaiguanjia:password@localhost:5432/chaiguanjia_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=chaiguanjia_dev
DATABASE_USER=chaiguanjia
DATABASE_PASSWORD=password

# Redis配置
REDIS_URL=redis://localhost:6379/0

# RabbitMQ配置
RABBITMQ_URL=amqp://chaiguanjia:password@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=chaiguanjia
RABBITMQ_PASSWORD=password

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
SECRET_KEY=your-secret-key-here

# CORS配置
ALLOWED_HOSTS=["*"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# AI服务配置（预留）
OPENAI_API_KEY=your-openai-key
AI_MODEL_NAME=gpt-3.5-turbo

# 环境标识
ENVIRONMENT=development
TESTING=false

# 安全配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=./uploads

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 第三方平台配置（预留）
XIANYU_APP_KEY=your-xianyu-app-key
XIANYU_APP_SECRET=your-xianyu-app-secret
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret