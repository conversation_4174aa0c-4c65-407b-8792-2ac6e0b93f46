# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

柴管家是一个面向知识类、教培类个人IP运营者的一站式私域运营解决方案。通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

## 架构设计

### 核心架构
- **架构模式**: 模块化单体 + 事件驱动连接器 (Modular Monolith with Event-Driven Connectors)
- **后端**: Python + FastAPI (模块化单体应用)
- **前端**: React + Vite 单页应用(SPA)
- **数据库**: PostgreSQL (主数据库) + ChromaDB (向量数据库)
- **消息队列**: RabbitMQ (连接器与后端异步通信)
- **连接器**: 独立Python进程，对接各第三方平台

### 技术栈选型理由
- **Python/FastAPI**: 团队熟悉，异步IO支持实时消息处理
- **React**: 团队熟悉，适合实时消息展示
- **PostgreSQL**: 功能强大，支持JSONB，Python生态首选
- **ChromaDB**: Python原生，轻量，易私有化部署
- **RabbitMQ**: 成熟可靠，支持事件驱动架构

## 开发哲学

### BDD开发流程
严格遵循三步走流程：
1. **编写行为剧本** (Gherkin Feature Files)
2. **编写自动化测试** (基于剧本生成失败测试)
3. **编写产品代码** (让测试通过的最精简代码)

### MVP实施策略
采用端到端故事切片，优先完成完整用户体验闭环。

## 核心业务模块

### 四大史诗 (MVP范围)
1. **核心渠道管理**: 多平台账号接入与管理
2. **统一消息工作台**: 跨平台消息聚合与回复
3. **AI副驾与知识库**: AI回复建议与FAQ管理
4. **AI托管与人工接管**: 自动回复与置信度判断

### 关键业务流程
- **接收消息**: 第三方平台 → 连接器 → MQ → 后端 → WebSocket推送前端
- **发送消息**: 前端 → 后端 → MQ → 连接器 → 第三方平台

## 数据模型设计

### 核心表结构
- **用户管理**: users, teams, team_members
- **渠道管理**: channel_instances (支持多平台多账号)
- **消息系统**: conversations, messages, media_assets
- **AI与知识库**: knowledge_base_faqs, interaction_events
- **电商模块**: products (SPU), product_variants (SKU), customers, orders, order_items

### 设计原则
- 字段命名采用下划线法
- 预留tenant_id字段支持未来SaaS化
- 关键业务表支持JSONB存储灵活数据

## 接口协议

### 消息队列协议 (RabbitMQ)
- **上行事件**: 外部平台事件标准化传递
- **下行指令**: 指令连接器执行操作
- 统一的event_id/command_id追踪机制

### REST API
- 前缀: `/api/v1`
- 核心端点: 认证、渠道管理、会话消息、知识库、商品订单等

### WebSocket
- 实时推送: 新消息、AI建议、订单更新、会话状态变更

## 部署与运维

### 部署模式
- **初期**: Docker + Docker Compose 私有化部署
- **未来**: 支持多租户SaaS平台平滑演进

### 性能要求
- 消息同步延迟 < 5秒
- AI响应时间 < 2秒
- 支持单IP千条私信并发

## 质量保证

### 完成定义 (DoD)
1. 通过CI/CD所有自动化检查
2. 所有验收标准通过自动化测试
3. 单元测试覆盖率 ≥ 85%
4. 代码已合并主干分支
5. API文档自动生成并更新

### 验收标准
- 结合可视化流程图和Gherkin剧本定义
- 每个功能都有明确的行为描述

## 开发迭代

### 冲刺周期
2周一个冲刺，按史诗逐步实现MVP功能

### 路线图重点
1. 环境搭建与CI/CD
2. 闲鱼连接器集成(技术验证)
3. 知识库与AI副驾
4. AI托管与人工接管

## 安全考虑

- 用户密码加盐哈希存储
- 第三方平台凭证加密存储
- 所有数据传输使用TLS 1.2+
- 敏感信息不得写入日志或提交代码

## 开发规范引用

### 遵循项目开发规范
在进行任何代码开发时，必须严格遵循以下规范文档：

1. **柴管家开发规范** - `项目开发规范/柴管家开发规范.md`
   - **何时遵守**: 所有开发任务的全生命周期
   - **核心要求**: BDD三步走流程（Gherkin剧本 → 自动化测试 → 产品代码）
   - **质量标准**: 测试覆盖率 ≥ 85%，代码审查必须，持续集成

2. **高效能编码规范** - `项目开发规范/高效能编码规范.md`
   - **何时遵守**: 编写任何产品代码时
   - **核心理念**: KISS & YAGNI（追求简单）、DRY（拒绝重复）、高内聚低耦合
   - **SOLID原则**: 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置

3. **命名规范 (GNC-AIDD)** - `项目开发规范/命名规范 (GNC-AIDD).md`
   - **何时遵守**: 命名变量、函数、类、文件、数据库字段时
   - **适用范围**: Python代码、数据库设计、API端点、文件命名

4. **模块化单体架构设计规范** - `项目开发规范/模块化单体架构设计规范.md`
   - **何时遵守**: 设计系统架构、模块划分、服务边界时
   - **核心原则**: 模块化解耦、事件驱动、依赖管理、可测试性

5. **HeroUI使用指南** - `项目开发规范/heroui使用指南.md`
   - **何时遵守**: 开发前端验证界面和UI组件时
   - **适用场景**: 验证界面设计、组件选择、界面交互

### 规范应用优先级
1. **必须遵守**: 柴管家开发规范（BDD流程和质量标准）
2. **代码质量**: 高效能编码规范（SOLID原则和设计哲学）
3. **命名一致性**: 命名规范（所有标识符命名）
4. **架构设计**: 模块化单体架构设计规范（系统设计时）
5. **UI实现**: HeroUI使用指南（前端界面开发时）

### 规范冲突处理
- 当不同规范出现冲突时，以**柴管家开发规范**为最高优先级
- 如有疑问，参考**高效能编码规范**的核心哲学进行判断
- 具体技术细节遵循对应的专项规范

## 注意事项

- 优先使用成熟开源方案降低成本
- 第三方API调用成本由终端用户承担
- 架构设计优先考虑稳健性与可扩展性
- 支持私有化部署到SaaS平台的平滑演进