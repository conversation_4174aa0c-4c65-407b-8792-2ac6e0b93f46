# 柴管家：多平台聚合智能客服系统

## 项目概述

柴管家是一个面向知识类、教培类个人IP运营者的一站式私域运营解决方案。通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

## 技术架构

### 核心技术栈
- **后端**: Python 3.11+ + FastAPI
- **前端**: React + Vite 
- **数据库**: PostgreSQL (主数据库) + ChromaDB (向量数据库)
- **消息队列**: RabbitMQ
- **缓存**: Redis
- **容器化**: Docker + Docker Compose

### 架构模式
- **模块化单体** + 事件驱动连接器
- **BDD驱动开发** (Behavior-Driven Development)
- **测试覆盖率** ≥ 85%

## 快速开始

### 环境要求
- Docker & Docker Compose
- Python 3.11+
- Git

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone <repository-url>
cd chaiguanjia_cc_8.3
```

2. **复制环境配置**
```bash
cp .env.example .env
# 根据需要修改 .env 文件中的配置
```

3. **启动开发环境**
```bash
docker-compose up --build
```

4. **验证服务**
- API文档: http://localhost:8000/docs
- 后端健康检查: http://localhost:8000/health
- RabbitMQ管理界面: http://localhost:15672 (chaiguanjia/password)
- 验证界面: http://localhost:5000

### 开发工作流

1. **运行测试**
```bash
./scripts/run_tests.sh
```

2. **代码格式化**
```bash
cd backend
black app tests
isort app tests
flake8 app tests
```

3. **数据库迁移**
```bash
cd backend
alembic upgrade head
```

## 项目结构

```
chaiguanjia_cc_8.3/
├── backend/                    # 后端应用
│   ├── app/                   # FastAPI应用
│   │   ├── core/             # 核心模块
│   │   ├── models/           # 数据模型
│   │   ├── schemas/          # Pydantic模式
│   │   ├── api/              # API路由
│   │   ├── services/         # 业务逻辑
│   │   ├── utils/            # 工具函数
│   │   └── workers/          # 后台任务
│   ├── alembic/              # 数据库迁移
│   ├── requirements.txt      # Python依赖
│   └── Dockerfile           # 容器镜像
├── connectors/                # 连接器模块
│   ├── base/                 # 基础连接器
│   ├── xianyu/              # 闲鱼连接器
│   └── wechat/              # 微信连接器
├── features/                  # BDD规范
│   ├── epic01_channel_management/
│   ├── epic02_unified_workspace/
│   ├── epic03_ai_copilot/
│   └── epic04_ai_hosting/
├── tests/                     # 测试代码
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   ├── bdd/                 # BDD测试
│   ├── fixtures/            # 测试数据
│   └── e2e/                 # 端到端测试
├── verification_ui/           # 验证界面
├── scripts/                   # 工具脚本
├── docs/                      # 技术文档
├── .github/                   # GitHub配置
└── docker-compose.yml        # 开发环境配置
```

## 开发指南

### BDD开发流程
本项目严格遵循BDD三步走流程：

1. **编写行为剧本** (Gherkin Feature Files)
   - 在 `features/` 目录下编写 `.feature` 文件
   - 使用中文描述用户行为和期望

2. **编写自动化测试** (基于剧本生成失败测试)
   - 在 `tests/bdd/` 目录下实现测试步骤
   - 确保测试最初是失败的

3. **编写产品代码** (让测试通过的最精简代码)
   - 在 `backend/app/` 目录下实现业务逻辑
   - 只写让测试通过的最少代码

### 代码质量标准
- **测试覆盖率**: ≥ 85%
- **代码格式**: Black + isort + flake8
- **类型检查**: mypy
- **安全扫描**: bandit + safety

### 提交规范
- 遵循 Conventional Commits 规范
- 所有代码必须通过CI/CD检查
- PR必须经过代码审查

## 史诗与路线图

### MVP阶段 (4个史诗)
1. **Epic 1**: 核心渠道管理 - 多平台账号接入与管理
2. **Epic 2**: 统一消息工作台 - 跨平台消息聚合与回复
3. **Epic 3**: AI副驾与知识库 - AI回复建议与FAQ管理
4. **Epic 4**: AI托管与人工接管 - 自动回复与置信度判断

### 当前状态
- ✅ Sprint 0: 基础设施搭建 (完成)
- 🔄 Sprint 1: Epic 1 开发中
- ⏳ Sprint 2-4: 待开发

## 贡献指南

### 开发环境配置
1. Fork 项目到个人仓库
2. 创建功能分支: `git checkout -b feature/your-feature`
3. 提交代码: `git commit -m 'feat: add some feature'`
4. 推送分支: `git push origin feature/your-feature`
5. 创建 Pull Request

### Issue 和 PR 模板
- 使用项目提供的 Issue 模板创建问题
- 使用 PR 模板提交代码变更
- 确保所有 CI 检查通过

## 部署

### 生产环境
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产服务
docker-compose -f docker-compose.prod.yml up -d
```

### 环境变量
详见 `.env.example` 文件中的配置说明。

## 许可证

[MIT License](LICENSE)

## 支持

如有问题或建议，请创建 Issue 或联系项目维护者。