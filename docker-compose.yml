version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./features:/app/features
      - ./tests:/app/tests
    environment:
      - DATABASE_URL=*****************************************/chaiguanjia_dev
      - REDIS_URL=redis://redis:6379/0
      - RABBITMQ_URL=amqp://chaiguanjia:password@rabbitmq:5672/
    depends_on:
      - db
      - redis
      - rabbitmq
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # PostgreSQL数据库
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: chaiguanjia_dev
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia
      RABBITMQ_DEFAULT_PASS: password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # 验证界面服务
  verification:
    build:
      context: ./verification_ui
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./verification_ui:/app
    environment:
      - BACKEND_API_URL=http://backend:8000
    depends_on:
      - backend

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data: