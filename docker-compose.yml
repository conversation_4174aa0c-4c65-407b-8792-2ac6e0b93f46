version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "7000:7000"
    volumes:
      - ./backend:/app
      - ./features:/app/features
      - ./tests:/app/tests
    environment:
      - DATABASE_URL=*****************************************/chaiguanjia_dev
      - REDIS_URL=redis://redis:6379/0
      - RABBITMQ_URL=amqp://chaiguanjia:password@rabbitmq:5672/
      - API_PORT=7000
      - API_DEBUG=true
      - LOG_LEVEL=INFO
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    command: uvicorn app.main:app --host 0.0.0.0 --port 7000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: chaiguanjia_dev
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chaiguanjia -d chaiguanjia_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia
      RABBITMQ_DEFAULT_PASS: password
      RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS: "-rabbit disk_free_limit **********"
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 验证界面服务
  verification:
    build:
      context: ./verification_ui
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./verification_ui:/app
    environment:
      - BACKEND_API_URL=http://backend:7000
    depends_on:
      - backend

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data: