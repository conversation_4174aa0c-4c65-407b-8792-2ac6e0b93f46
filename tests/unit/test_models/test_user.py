"""
测试用户模型
"""

import pytest
from datetime import datetime, timedelta
from app.models.user import User


class TestUser:
    """用户模型测试类"""
    
    def test_user_creation(self):
        """测试用户创建"""
        user = User.create_with_defaults(
            email="<EMAIL>",
            full_name="测试用户",
            password_hash="hashed_password",
            salt="random_salt"
        )
        
        assert user.email == "<EMAIL>"
        assert user.full_name == "测试用户"
        assert user.is_active is True
        assert user.is_deleted is False
        assert user.is_email_verified is False
        assert user.login_attempts == "0"
    
    def test_set_password(self):
        """测试设置密码"""
        user = User()
        user.set_password("test_password_123")
        
        assert user.password_hash is not None
        assert user.salt is not None
        assert len(user.salt) == 32  # 16字节的hex字符串
        assert len(user.password_hash) == 64  # SHA256哈希的hex字符串
    
    def test_verify_email(self):
        """测试邮箱验证"""
        user = User()
        assert user.is_email_verified is False
        assert user.email_verified_at is None
        
        user.verify_email()
        
        assert user.is_email_verified is True
        assert user.email_verified_at is not None
        assert isinstance(user.email_verified_at, datetime)
    
    def test_lock_account(self):
        """测试账户锁定"""
        user = User()
        lock_until = datetime.utcnow() + timedelta(hours=1)
        
        user.lock_account(lock_until)
        
        assert user.is_locked is True
        assert user.locked_until == lock_until
        assert user.is_account_locked is True
    
    def test_unlock_account(self):
        """测试账户解锁"""
        user = User()
        user.lock_account()
        user.login_attempts = "5"
        
        user.unlock_account()
        
        assert user.is_locked is False
        assert user.locked_until is None
        assert user.login_attempts == "0"
        assert user.is_account_locked is False
    
    def test_record_login(self):
        """测试登录记录"""
        user = User()
        user.login_attempts = "3"
        ip_address = "***********"
        
        user.record_login(ip_address)
        
        assert user.last_login_at is not None
        assert user.last_login_ip == ip_address
        assert user.login_attempts == "0"
    
    def test_increment_login_attempts(self):
        """测试增加登录失败次数"""
        user = User()
        assert user.login_attempts == "0"
        
        user.increment_login_attempts()
        assert user.login_attempts == "1"
        
        user.increment_login_attempts()
        assert user.login_attempts == "2"
    
    def test_should_lock_account(self):
        """测试是否应该锁定账户"""
        user = User()
        
        # 默认情况下不应该锁定
        assert user.should_lock_account() is False
        
        # 设置失败次数达到阈值
        user.login_attempts = "5"
        assert user.should_lock_account() is True
        
        # 自定义阈值
        user.login_attempts = "3"
        assert user.should_lock_account(max_attempts=3) is True
        assert user.should_lock_account(max_attempts=5) is False
    
    def test_update_preferences(self):
        """测试更新用户偏好"""
        user = User()
        
        # 初始偏好为空
        user.update_preferences({"theme": "dark", "language": "zh-CN"})
        assert user.preferences["theme"] == "dark"
        assert user.preferences["language"] == "zh-CN"
        
        # 更新部分偏好
        user.update_preferences({"theme": "light"})
        assert user.preferences["theme"] == "light"
        assert user.preferences["language"] == "zh-CN"  # 保持不变
    
    def test_get_preference(self):
        """测试获取用户偏好"""
        user = User()
        user.preferences = {"theme": "dark", "notifications": True}
        
        assert user.get_preference("theme") == "dark"
        assert user.get_preference("notifications") is True
        assert user.get_preference("nonexistent") is None
        assert user.get_preference("nonexistent", "default") == "default"
    
    def test_display_name(self):
        """测试显示名称"""
        user = User()
        
        # 使用full_name
        user.full_name = "张三"
        assert user.display_name == "张三"
        
        # 使用username（当full_name为空时）
        user.full_name = None
        user.username = "zhangsan"
        assert user.display_name == "zhangsan"
        
        # 使用email（当full_name和username都为空时）
        user.username = None
        user.email = "<EMAIL>"
        assert user.display_name == "zhangsan"
    
    def test_is_account_locked_property(self):
        """测试账户锁定状态属性"""
        user = User()
        
        # 默认未锁定
        assert user.is_account_locked is False
        
        # 永久锁定
        user.is_locked = True
        user.locked_until = None
        assert user.is_account_locked is True
        
        # 临时锁定且未到期
        future_time = datetime.utcnow() + timedelta(hours=1)
        user.locked_until = future_time
        assert user.is_account_locked is True
        
        # 临时锁定但已到期
        past_time = datetime.utcnow() - timedelta(hours=1)
        user.locked_until = past_time
        assert user.is_account_locked is False
    
    def test_to_dict_exclude_sensitive(self):
        """测试转换为字典（排除敏感信息）"""
        user = User.create_with_defaults(
            email="<EMAIL>",
            full_name="测试用户",
            password_hash="hashed_password",
            salt="random_salt"
        )
        user.login_attempts = "3"
        
        # 排除敏感信息
        user_dict = user.to_dict(exclude_sensitive=True)
        assert "password_hash" not in user_dict
        assert "salt" not in user_dict
        assert "login_attempts" not in user_dict
        assert user_dict["email"] == "<EMAIL>"
        
        # 包含敏感信息
        user_dict = user.to_dict(exclude_sensitive=False)
        assert "password_hash" in user_dict
        assert "salt" in user_dict
        assert "login_attempts" in user_dict
    
    def test_soft_delete(self):
        """测试软删除"""
        user = User()
        assert user.is_deleted is False
        assert user.deleted_at is None
        
        user.soft_delete()
        
        assert user.is_deleted is True
        assert user.deleted_at is not None
        assert isinstance(user.deleted_at, datetime)
    
    def test_restore(self):
        """测试恢复软删除"""
        user = User()
        user.soft_delete()
        
        user.restore()
        
        assert user.is_deleted is False
        assert user.deleted_at is None