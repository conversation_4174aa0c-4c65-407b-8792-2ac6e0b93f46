"""
测试配置模块
"""

import pytest
import os
from unittest.mock import patch
from app.core.config import Settings


def test_settings_creation():
    """测试配置创建"""
    settings = Settings()
    assert settings.API_PORT == 7000
    assert settings.API_DEBUG is True
    assert settings.LOG_LEVEL == "INFO"


def test_settings_with_env_override():
    """测试环境变量覆盖"""
    with patch.dict(os.environ, {
        'API_PORT': '8080',
        'API_DEBUG': 'false',
        'LOG_LEVEL': 'DEBUG'
    }):
        settings = Settings()
        assert settings.API_PORT == 8080
        assert settings.API_DEBUG is False
        assert settings.LOG_LEVEL == "DEBUG"


def test_database_url_validation():
    """测试数据库URL验证"""
    # 有效的PostgreSQL URL
    settings = Settings(DATABASE_URL="postgresql://user:pass@localhost:5432/db")
    assert settings.DATABASE_URL.startswith("postgresql://")
    
    # 无效的URL应该抛出异常
    with pytest.raises(ValueError):
        Settings(DATABASE_URL="invalid_url")


def test_allowed_hosts_parsing():
    """测试允许的主机解析"""
    settings = Settings()
    assert "*" in settings.ALLOWED_HOSTS
    
    # 测试自定义主机列表
    with patch.dict(os.environ, {'ALLOWED_HOSTS': 'localhost,127.0.0.1'}):
        settings = Settings()
        # 这里需要实现ALLOWED_HOSTS的解析逻辑


def test_redis_url_format():
    """测试Redis URL格式"""
    settings = Settings()
    assert settings.REDIS_URL.startswith("redis://")


def test_rabbitmq_url_format():
    """测试RabbitMQ URL格式"""
    settings = Settings()
    assert settings.RABBITMQ_URL.startswith("amqp://")