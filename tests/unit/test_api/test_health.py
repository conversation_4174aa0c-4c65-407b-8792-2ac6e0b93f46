"""
测试健康检查API
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_basic_health_check():
    """测试基础健康检查"""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "chaiguanjia-api"
    assert data["version"] == "1.0.0"
    assert "timestamp" in data


def test_root_endpoint():
    """测试根端点"""
    response = client.get("/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "running"
    assert data["version"] == "1.0.0"
    assert "message" in data
    assert "docs_url" in data


def test_main_health_endpoint():
    """测试主健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "chaiguanjia-api"


@patch('app.api.v1.health.db_manager')
def test_database_health_check_success(mock_db_manager):
    """测试数据库健康检查成功"""
    # 模拟数据库健康检查成功
    mock_db_manager.health_check.return_value = {
        "status": "healthy",
        "response_time_ms": 10.5,
        "database_info": {"type": "PostgreSQL"},
        "connection_pool": {"size": 5, "checked_out": 1, "overflow": 0}
    }
    
    response = client.get("/api/v1/health/db")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"
    assert "details" in data


@patch('app.api.v1.health.db_manager')
def test_database_health_check_failure(mock_db_manager):
    """测试数据库健康检查失败"""
    # 模拟数据库健康检查失败
    mock_db_manager.health_check.return_value = {
        "status": "unhealthy",
        "error": "Connection failed",
        "error_type": "ConnectionError"
    }
    
    response = client.get("/api/v1/health/db")
    assert response.status_code == 503
    
    data = response.json()
    assert data["detail"]["status"] == "unhealthy"
    assert data["detail"]["database"] == "disconnected"


@patch('app.api.v1.health.redis.from_url')
def test_redis_health_check_success(mock_redis):
    """测试Redis健康检查成功"""
    # 模拟Redis连接成功
    mock_redis_instance = MagicMock()
    mock_redis.return_value = mock_redis_instance
    mock_redis_instance.ping.return_value = True
    
    response = client.get("/api/v1/health/redis")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["redis"] == "connected"
    assert "response_time_ms" in data


@patch('app.api.v1.health.redis.from_url')
def test_redis_health_check_failure(mock_redis):
    """测试Redis健康检查失败"""
    # 模拟Redis连接失败
    mock_redis.side_effect = Exception("Connection refused")
    
    response = client.get("/api/v1/health/redis")
    assert response.status_code == 503
    
    assert "Redis连接失败" in response.json()["detail"]


@patch('app.api.v1.health.pika.BlockingConnection')
def test_rabbitmq_health_check_success(mock_connection):
    """测试RabbitMQ健康检查成功"""
    # 模拟RabbitMQ连接成功
    mock_conn_instance = MagicMock()
    mock_connection.return_value = mock_conn_instance
    
    response = client.get("/api/v1/health/rabbitmq")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["rabbitmq"] == "connected"
    assert "response_time_ms" in data


@patch('app.api.v1.health.pika.BlockingConnection')
def test_rabbitmq_health_check_failure(mock_connection):
    """测试RabbitMQ健康检查失败"""
    # 模拟RabbitMQ连接失败
    mock_connection.side_effect = Exception("Connection refused")
    
    response = client.get("/api/v1/health/rabbitmq")
    assert response.status_code == 503
    
    assert "RabbitMQ连接失败" in response.json()["detail"]


@patch('app.api.v1.health.check_database')
@patch('app.api.v1.health.check_redis')
@patch('app.api.v1.health.check_rabbitmq')
def test_full_health_check_all_healthy(mock_rabbitmq, mock_redis, mock_db):
    """测试完整健康检查 - 所有服务健康"""
    # 模拟所有服务健康
    mock_db.return_value = {"status": "healthy"}
    mock_redis.return_value = {"connected": True, "response_time_ms": 5.0}
    mock_rabbitmq.return_value = {"connected": True, "response_time_ms": 10.0}
    
    response = client.get("/api/v1/health/full")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["summary"]["total_checks"] == 3
    assert data["summary"]["passed_checks"] == 3
    assert data["summary"]["failed_checks"] == 0
    assert data["summary"]["health_score"] == 100.0


@patch('app.api.v1.health.check_database')
@patch('app.api.v1.health.check_redis')
@patch('app.api.v1.health.check_rabbitmq')
def test_full_health_check_partial_failure(mock_rabbitmq, mock_redis, mock_db):
    """测试完整健康检查 - 部分服务失败"""
    # 模拟部分服务失败
    mock_db.return_value = {"status": "healthy"}
    mock_redis.side_effect = Exception("Redis connection failed")
    mock_rabbitmq.return_value = {"connected": True, "response_time_ms": 10.0}
    
    response = client.get("/api/v1/health/full")
    assert response.status_code == 503
    
    data = response.json()["detail"]
    assert data["status"] == "unhealthy"
    assert data["summary"]["total_checks"] == 3
    assert data["summary"]["passed_checks"] == 2
    assert data["summary"]["failed_checks"] == 1
    assert data["summary"]["health_score"] == 66.7