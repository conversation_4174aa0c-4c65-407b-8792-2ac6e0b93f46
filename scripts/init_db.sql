-- 柴管家数据库初始化脚本
-- 创建基础数据库结构和初始数据

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建基础架构（如果不存在）
DO $$
BEGIN
    -- 检查数据库是否已初始化
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        -- 创建用户表（基础结构，用于健康检查）
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email VARCHAR(255) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 插入系统用户（用于测试）
        INSERT INTO users (email, full_name) VALUES 
        ('<EMAIL>', '系统用户'),
        ('<EMAIL>', '管理员用户'),
        ('<EMAIL>', '测试用户')
        ON CONFLICT (email) DO NOTHING;
        
        RAISE NOTICE '数据库初始化完成';
    ELSE
        RAISE NOTICE '数据库已存在，跳过初始化';
    END IF;
END
$$;