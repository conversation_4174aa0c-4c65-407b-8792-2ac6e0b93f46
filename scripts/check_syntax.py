#!/usr/bin/env python3
"""
检查Python代码语法
"""

import ast
import os
import sys
from pathlib import Path

def check_python_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        return True, None
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / "backend"
    
    print("🔍 检查Python代码语法...")
    
    error_count = 0
    total_files = 0
    
    # 检查backend目录下的所有Python文件
    for py_file in backend_dir.rglob("*.py"):
        if "__pycache__" in str(py_file):
            continue
            
        total_files += 1
        is_valid, error = check_python_syntax(py_file)
        
        if is_valid:
            print(f"✅ {py_file.relative_to(project_root)}")
        else:
            print(f"❌ {py_file.relative_to(project_root)}: {error}")
            error_count += 1
    
    print(f"\n📊 检查结果:")
    print(f"   总文件数: {total_files}")
    print(f"   错误文件数: {error_count}")
    print(f"   成功率: {((total_files - error_count) / total_files * 100):.1f}%")
    
    if error_count > 0:
        print("❌ 存在语法错误，请修复后再启动服务")
        sys.exit(1)
    else:
        print("✅ 所有Python文件语法正确")

if __name__ == "__main__":
    main()