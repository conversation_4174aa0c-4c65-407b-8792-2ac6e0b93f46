#!/usr/bin/env python3
"""
验证Docker配置文件
"""

import yaml
import os
import sys
from pathlib import Path

def validate_docker_compose():
    """验证docker-compose.yml配置"""
    print("🐳 验证 docker-compose.yml 配置...")
    
    project_root = Path(__file__).parent.parent
    compose_file = project_root / "docker-compose.yml"
    
    if not compose_file.exists():
        print("❌ docker-compose.yml 文件不存在")
        return False
    
    try:
        with open(compose_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查基本结构
        if 'services' not in config:
            print("❌ docker-compose.yml 缺少 services 配置")
            return False
        
        # 检查必需的服务
        required_services = ['backend', 'db', 'redis', 'rabbitmq', 'verification']
        for service in required_services:
            if service not in config['services']:
                print(f"❌ 缺少必需服务: {service}")
                return False
            else:
                print(f"✅ 服务配置正常: {service}")
        
        # 检查端口配置
        backend_ports = config['services']['backend'].get('ports', [])
        if '7000:7000' not in backend_ports:
            print("❌ 后端端口配置错误，应该是 7000:7000")
            return False
        
        print("✅ docker-compose.yml 配置验证通过")
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ docker-compose.yml 格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def validate_dockerfiles():
    """验证Dockerfile配置"""
    print("\n📦 验证 Dockerfile 配置...")
    
    project_root = Path(__file__).parent.parent
    
    # 验证后端Dockerfile
    backend_dockerfile = project_root / "backend" / "Dockerfile"
    if backend_dockerfile.exists():
        with open(backend_dockerfile, 'r') as f:
            content = f.read()
            if 'EXPOSE 7000' in content:
                print("✅ 后端 Dockerfile 端口配置正确")
            else:
                print("❌ 后端 Dockerfile 端口配置错误")
                return False
    else:
        print("❌ 后端 Dockerfile 不存在")
        return False
    
    # 验证验证界面Dockerfile
    verification_dockerfile = project_root / "verification_ui" / "Dockerfile"
    if verification_dockerfile.exists():
        print("✅ 验证界面 Dockerfile 存在")
    else:
        print("❌ 验证界面 Dockerfile 不存在")
        return False
    
    return True

def validate_env_config():
    """验证环境配置"""
    print("\n⚙️ 验证环境配置...")
    
    project_root = Path(__file__).parent.parent
    env_example = project_root / ".env.example"
    
    if not env_example.exists():
        print("❌ .env.example 文件不存在")
        return False
    
    with open(env_example, 'r') as f:
        content = f.read()
        
    required_vars = [
        'DATABASE_URL',
        'REDIS_URL', 
        'RABBITMQ_URL',
        'API_PORT',
        'SECRET_KEY'
    ]
    
    for var in required_vars:
        if var in content:
            print(f"✅ 环境变量配置正常: {var}")
        else:
            print(f"❌ 缺少环境变量: {var}")
            return False
    
    # 检查端口配置
    if 'API_PORT=7000' in content:
        print("✅ API端口配置正确")
    else:
        print("❌ API端口配置错误，应该是 7000")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 验证Docker开发环境配置...\n")
    
    success = True
    
    # 验证各个配置
    success &= validate_docker_compose()
    success &= validate_dockerfiles()
    success &= validate_env_config()
    
    print(f"\n📊 验证结果:")
    if success:
        print("✅ 所有Docker配置验证通过")
        print("🚀 可以继续启动开发环境")
    else:
        print("❌ 存在配置问题，请修复后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()