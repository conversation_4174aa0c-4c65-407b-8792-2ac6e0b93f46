#!/usr/bin/env python3
"""
准备开发环境
"""

import os
import shutil
from pathlib import Path

def create_env_file():
    """创建.env文件"""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("📋 创建 .env 文件...")
        shutil.copy(env_example, env_file)
        print("✅ .env 文件创建成功")
        return True
    elif env_file.exists():
        print("✅ .env 文件已存在")
        return True
    else:
        print("❌ .env.example 文件不存在，无法创建 .env")
        return False

def create_required_directories():
    """创建必需的目录"""
    project_root = Path(__file__).parent.parent
    
    required_dirs = [
        "backend/logs",
        "backend/uploads", 
        "verification_ui/logs"
    ]
    
    print("📁 创建必需目录...")
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目录创建: {dir_path}")

def validate_file_structure():
    """验证文件结构"""
    project_root = Path(__file__).parent.parent
    
    required_files = [
        "docker-compose.yml",
        "backend/Dockerfile",
        "backend/requirements.txt",
        "backend/app/main.py",
        "backend/app/core/config.py",
        "backend/app/core/database.py",
        "backend/app/api/v1/health.py",
        "verification_ui/Dockerfile",
        "verification_ui/app.py",
        "scripts/init_db.sql"
    ]
    
    print("🔍 验证文件结构...")
    missing_files = []
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 个必需文件")
        return False
    else:
        print("\n✅ 所有必需文件都存在")
        return True

def main():
    """主函数"""
    print("🛠️ 准备柴管家开发环境...\n")
    
    success = True
    
    # 创建环境文件
    success &= create_env_file()
    
    # 创建必需目录
    create_required_directories()
    
    # 验证文件结构
    success &= validate_file_structure()
    
    print(f"\n📊 环境准备结果:")
    if success:
        print("✅ 开发环境准备完成")
        print("🚀 可以启动Docker服务")
    else:
        print("❌ 环境准备失败，请检查缺失的文件")
        return False
    
    return True

if __name__ == "__main__":
    main()