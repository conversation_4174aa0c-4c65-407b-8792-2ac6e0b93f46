#!/bin/bash

echo "🔍 验证柴管家服务状态..."

# 检查Docker服务是否运行
echo "1. 检查Docker容器状态..."
docker-compose ps

echo ""
echo "2. 检查服务健康状态..."

# 检查后端API
echo "   📡 检查后端API..."
if curl -f -s http://localhost:7000/health > /dev/null; then
    echo "   ✅ 后端API正常运行"
else
    echo "   ❌ 后端API无法访问"
fi

# 检查数据库
echo "   🗄️  检查数据库连接..."
if curl -f -s http://localhost:7000/api/v1/health/db > /dev/null; then
    echo "   ✅ 数据库连接正常"
else
    echo "   ❌ 数据库连接失败"
fi

# 检查Redis
echo "   🔴 检查Redis连接..."
if curl -f -s http://localhost:7000/api/v1/health/redis > /dev/null; then
    echo "   ✅ Redis连接正常"
else
    echo "   ❌ Redis连接失败"
fi

# 检查RabbitMQ
echo "   🐰 检查RabbitMQ连接..."
if curl -f -s http://localhost:7000/api/v1/health/rabbitmq > /dev/null; then
    echo "   ✅ RabbitMQ连接正常"
else
    echo "   ❌ RabbitMQ连接失败"
fi

# 检查验证界面
echo "   🌐 检查验证界面..."
if curl -f -s http://localhost:5000/health > /dev/null; then
    echo "   ✅ 验证界面正常运行"
else
    echo "   ❌ 验证界面无法访问"
fi

echo ""
echo "3. 完整健康检查..."
response=$(curl -s http://localhost:7000/api/v1/health/full)
if echo "$response" | grep -q '"status": "healthy"'; then
    echo "   ✅ 所有服务健康状态正常"
else
    echo "   ❌ 部分服务存在问题"
    echo "   详细信息: $response"
fi

echo ""
echo "🎯 验证完成！"