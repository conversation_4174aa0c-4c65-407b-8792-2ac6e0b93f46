#!/bin/bash

echo "🚀 启动柴管家开发环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 复制环境配置（如果不存在）
if [ ! -f ".env" ]; then
    echo "📋 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 请根据需要修改 .env 文件中的配置"
fi

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动所有服务
echo "🔨 构建并启动所有服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 柴管家开发环境启动完成！"
echo ""
echo "📍 服务访问地址："
echo "   - API文档:        http://localhost:7000/docs"
echo "   - 后端健康检查:    http://localhost:7000/health"
echo "   - 验证界面:        http://localhost:5000"
echo "   - RabbitMQ管理:    http://localhost:15672 (用户名/密码: chaiguanjia/password)"
echo ""
echo "🔧 常用命令："
echo "   - 查看日志:        docker-compose logs -f"
echo "   - 停止服务:        docker-compose down"
echo "   - 重启服务:        docker-compose restart"
echo ""