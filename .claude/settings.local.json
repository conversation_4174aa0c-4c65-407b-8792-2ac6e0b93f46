{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(echo:*)", "Bash(pwd:*)", "<PERSON><PERSON>(whoami:*)", "Bash(which:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(wc:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "<PERSON><PERSON>(cut:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(tar:*)", "Bash(zip:*)", "Ba<PERSON>(unzip:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(git add:*)", "Bash(git status:*)", "Bash(git log:*)", "Bash(git diff:*)", "Bash(git branch:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "Bash(git pull:*)", "Bash(git push:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Ba<PERSON>(pip list:*)", "Bash(npm install:*)", "Bash(npm run:*)", "<PERSON><PERSON>(docker ps:*)", "Ba<PERSON>(docker images:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(docker-compose ps:*)", "Bash(pytest:*)", "Bash(black:*)", "Bash(isort:*)", "Bash(flake8:*)", "<PERSON><PERSON>(mypy:*)", "Bash(pip3 install:*)"], "deny": ["Bash(rm:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(sudo:*)", "<PERSON><PERSON>(su:*)", "<PERSON><PERSON>(chmod:*)", "Bash(chown:*)", "Bash(kill:*)", "<PERSON><PERSON>(killall:*)", "<PERSON><PERSON>(reboot:*)", "Bash(shutdown:*)", "<PERSON><PERSON>(dd:*)", "Bash(mkfs:*)", "Bash(fdisk:*)", "Bash(mount:*)", "Bash(umount:*)", "Bash(iptables:*)", "Bash(systemctl:*)", "Bash(service:*)"]}}